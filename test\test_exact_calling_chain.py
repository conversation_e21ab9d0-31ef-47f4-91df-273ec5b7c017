#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test the exact calling chain that produces the error
"""

import sys
import os
import pandas as pd
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from dynamic_gap_detector import HistoricalBreakthroughDetector
    
    # Create a detector
    detector = HistoricalBreakthroughDetector()
    
    # Create mock data similar to what would be passed in real scenario
    mock_stock_data = pd.DataFrame({
        '名称': ['海昌新材', '翰宇药业', '腾远钴业', '石头科技'],
        '代码': ['300885', '300199', '301219', '688169'],
        '今日主力净流入-净额': [1000000, 2000000, 1500000, 3000000],
        '大于历史资金流入': ['10天', '5天', '', '15天']
    })
    
    mock_market_snapshot = {
        'stock_gap_leader': '海昌新材',
        'sector_gap_leaders': ['概念1', '概念2'],
        'sector_rankings': {'概念1': 1, '概念2': 2}
    }
    
    print("=== Testing the exact calling chain ===")
    
    try:
        signals = detector.detect_signals(mock_stock_data, "10:06:00", mock_market_snapshot)
        print(f"成功检测到 {len(signals)} 个信号")
        for signal in signals:
            print(f"  - {signal.get('股票名称', 'Unknown')}: {signal.get('信号类型', 'Unknown')}")
    except Exception as e:
        print(f"错误发生在 detect_signals: {e}")
        if "ambiguous" in str(e):
            print("❌ 找到了pandas Series布尔值歧义错误!")
        
        import traceback
        print("完整错误堆栈:")
        traceback.print_exc()
        
except ImportError as e:
    print(f"导入错误: {e}")