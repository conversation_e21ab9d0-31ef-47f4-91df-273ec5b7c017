import akshare as ak
import sqlite3
import pandas as pd
import time
import logging
from datetime import datetime, timedelta
import uuid
import re
import requests
from requests.exceptions import ConnectionError, Timeout
from tqdm import tqdm
import re
import efinance as ef


# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 文件头配置：数据获取类型
DATA_TYPE = 'all'  # 'single' 为单个股票，'all' 为所有股票
SINGLE_STOCK = '002052'  # 默认股票：掌阅科技
SINGLE_MARKET = 'sh'  # 默认市场：上海证券交易所
DB_NAME = 'fund_flow.db'  # 数据库文件名
DB_NAME_MINUTE = 'fund_flow_minute.db'  # 分钟级数据库文件名

# 运行模式配置
RUN_MODE = 'data'  # 'data': 获取数据和计算指标模式（默认）, 'ranking': 排行榜模式, 'indicators': 指标模式

# 数据类型配置
# DATA_TYPE = 'all'  # 'single' 为单个股票，'all' 为所有股票

# 排行榜模式配置
RANKING_MODE = 'incremental'  # 'full': 生成全部时间范围排行榜, 'incremental': 增量更新排行榜（默认）
RANKING_TOP_N = 500  # 每个排行榜保存前N名
RANKING_DISPLAY_N = 20  # 显示前N名作为示例

# 排行榜指标配置
RANKING_METRICS = [
    'main_net_inflow',
    'main_net_ratio',
    'super_large_net_inflow',
    'super_large_net_ratio',
    'large_net_inflow',
    'large_net_ratio',
    'medium_net_inflow',
    'medium_net_ratio',
    'small_net_inflow',
    'small_net_ratio'
]


# 数据库初始化与表结构创建
def init_database():
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    # 查询现有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    existing_tables = {row[0] for row in cursor.fetchall()}

    # 表定义
    tables = {
        'daily_fund_flow': {
            'create_table': '''
                CREATE TABLE daily_fund_flow (
                    stock_code TEXT,
                    date TEXT,
                    close_price REAL,
                    change_pct REAL,
                    main_net_inflow REAL,
                    main_net_ratio REAL,
                    super_large_net_inflow REAL,
                    super_large_net_ratio REAL,
                    large_net_inflow REAL,
                    large_net_ratio REAL,
                    medium_net_inflow REAL,
                    medium_net_ratio REAL,
                    small_net_inflow REAL,
                    small_net_ratio REAL,
                    PRIMARY KEY (stock_code, date)
                )
            ''',
            'indexes': [
                ('idx_daily_fund_flow_stock_date', 'CREATE INDEX idx_daily_fund_flow_stock_date ON daily_fund_flow (stock_code, date)')
            ]
        },
        'rank_data': {
            'create_table': '''
                CREATE TABLE rank_data (
                    stock_code TEXT,
                    rank_type TEXT,
                    date TEXT,
                    main_net_inflow REAL,
                    main_net_ratio REAL,
                    super_large_net_inflow REAL,
                    super_large_net_ratio REAL,
                    large_net_inflow REAL,
                    large_net_ratio REAL,
                    medium_net_inflow REAL,
                    medium_net_ratio REAL,
                    small_net_inflow REAL,
                    small_net_ratio REAL,
                    PRIMARY KEY (stock_code, rank_type, date)
                )
            ''',
            'indexes': [
                ('idx_rank_data_stock_rank', 'CREATE INDEX idx_rank_data_stock_rank ON rank_data (stock_code, rank_type)')
            ]
        },
        'stock_stats_up': {
            'create_table': '''
                CREATE TABLE stock_stats_up (
                    stock_code TEXT,
                    date TEXT,
                    avg_main_net_inflow REAL,
                    avg_main_net_ratio REAL,
                    avg_super_large_net_inflow REAL,
                    avg_super_large_net_ratio REAL,
                    avg_large_net_inflow REAL,
                    avg_large_net_ratio REAL,
                    avg_medium_net_inflow REAL,
                    avg_medium_net_ratio REAL,
                    avg_small_net_inflow REAL,
                    avg_small_net_ratio REAL,
                    record_count INTEGER,
                    PRIMARY KEY (stock_code, date)
                )
            ''',
            'indexes': [
                ('idx_stock_stats_up_stock_date', 'CREATE INDEX idx_stock_stats_up_stock_date ON stock_stats_up (stock_code, date)')
            ]
        },
        'stock_stats_down': {
            'create_table': '''
                CREATE TABLE stock_stats_down (
                    stock_code TEXT,
                    date TEXT,
                    avg_main_net_inflow REAL,
                    avg_main_net_ratio REAL,
                    avg_super_large_net_inflow REAL,
                    avg_super_large_net_ratio REAL,
                    avg_large_net_inflow REAL,
                    avg_large_net_ratio REAL,
                    avg_medium_net_inflow REAL,
                    avg_medium_net_ratio REAL,
                    avg_small_net_inflow REAL,
                    avg_small_net_ratio REAL,
                    record_count INTEGER,
                    PRIMARY KEY (stock_code, date)
                )
            ''',
            'indexes': [
                ('idx_stock_stats_down_stock_date', 'CREATE INDEX idx_stock_stats_down_stock_date ON stock_stats_down (stock_code, date)')
            ]
        },
        'stock_stats_threshold': {
            'create_table': '''
                CREATE TABLE stock_stats_threshold (
                    stock_code TEXT,
                    threshold_type TEXT,
                    avg_main_net_inflow REAL,
                    avg_super_large_net_inflow REAL,
                    avg_large_net_inflow REAL,
                    record_count INTEGER,
                    PRIMARY KEY (stock_code, threshold_type)
                )
            ''',
            'indexes': [
                ('idx_stock_stats_threshold_stock_type', 'CREATE INDEX idx_stock_stats_threshold_stock_type ON stock_stats_threshold (stock_code, threshold_type)')
            ]
        },
        'stock_stats_extremes': {
            'create_table': '''
                CREATE TABLE stock_stats_extremes (
                    stock_code TEXT,
                    metric TEXT,
                    max_value REAL,
                    max_date TEXT,
                    min_value REAL,
                    min_date TEXT,
                    PRIMARY KEY (stock_code, metric)
                )
            ''',
            'indexes': [
                ('idx_stock_stats_extremes_stock_metric', 'CREATE INDEX idx_stock_stats_extremes_stock_metric ON stock_stats_extremes (stock_code, metric)')
            ]
        },
        'stock_stats_yearly': {
            'create_table': '''
                CREATE TABLE stock_stats_yearly (
                    stock_code TEXT,
                    year TEXT,
                    period_type TEXT,
                    avg_main_net_inflow REAL,
                    avg_main_net_ratio REAL,
                    avg_super_large_net_inflow REAL,
                    avg_super_large_net_ratio REAL,
                    avg_large_net_inflow REAL,
                    avg_large_net_ratio REAL,
                    avg_medium_net_inflow REAL,
                    avg_medium_net_ratio REAL,
                    avg_small_net_inflow REAL,
                    avg_small_net_ratio REAL,
                    record_count INTEGER,
                    PRIMARY KEY (stock_code, year, period_type)
                )
            ''',
            'indexes': [
                ('idx_stock_stats_yearly_stock_year', 'CREATE INDEX idx_stock_stats_yearly_stock_year ON stock_stats_yearly (stock_code, year)')
            ]
        },
        'stock_stats_threshold_yearly': {
            'create_table': '''
                CREATE TABLE stock_stats_threshold_yearly (
                    stock_code TEXT,
                    year TEXT,
                    threshold_type TEXT,
                    avg_main_net_inflow REAL,
                    avg_super_large_net_inflow REAL,
                    avg_large_net_inflow REAL,
                    record_count INTEGER,
                    PRIMARY KEY (stock_code, year, threshold_type)
                )
            ''',
            'indexes': [
                ('idx_stock_stats_threshold_yearly_stock_year', 'CREATE INDEX idx_stock_stats_threshold_yearly_stock_year ON stock_stats_threshold_yearly (stock_code, year)')
            ]
        },
        'stock_stats_extremes_yearly': {
            'create_table': '''
                CREATE TABLE stock_stats_extremes_yearly (
                    stock_code TEXT,
                    year TEXT,
                    metric TEXT,
                    max_value REAL,
                    max_date TEXT,
                    min_value REAL,
                    min_date TEXT,
                    PRIMARY KEY (stock_code, year, metric)
                )
            ''',
            'indexes': [
                ('idx_stock_stats_extremes_yearly_stock_year', 'CREATE INDEX idx_stock_stats_extremes_yearly_stock_year ON stock_stats_extremes_yearly (stock_code, year)')
            ]
        },
        'stock_metadata': {
            'create_table': '''
                CREATE TABLE stock_metadata (
                    stock_code TEXT PRIMARY KEY,
                    last_data_update TEXT,
                    last_stats_update TEXT,
                    last_ranking_update TEXT
                )
            ''',
            'indexes': []
        },
        'daily_rankings': {
            'create_table': '''
                CREATE TABLE daily_rankings (
                    date TEXT,
                    metric TEXT,
                    rank_position INTEGER,
                    stock_code TEXT,
                    stock_name TEXT,
                    metric_value REAL,
                    PRIMARY KEY (date, metric, rank_position)
                )
            ''',
            'indexes': [
                ('idx_daily_rankings_date_metric', 'CREATE INDEX idx_daily_rankings_date_metric ON daily_rankings (date, metric)'),
                ('idx_daily_rankings_stock', 'CREATE INDEX idx_daily_rankings_stock ON daily_rankings (stock_code)')
            ]
        },
        'historical_max_rankings': {
            'create_table': '''
                CREATE TABLE historical_max_rankings (
                    date TEXT,
                    metric TEXT,
                    rank_position INTEGER,
                    stock_code TEXT,
                    stock_name TEXT,
                    current_value REAL,
                    historical_max_value REAL,
                    historical_max_date TEXT,
                    is_historical_max INTEGER,
                    PRIMARY KEY (date, metric, rank_position)
                )
            ''',
            'indexes': [
                ('idx_historical_max_rankings_date_metric', 'CREATE INDEX idx_historical_max_rankings_date_metric ON historical_max_rankings (date, metric)'),
                ('idx_historical_max_rankings_stock', 'CREATE INDEX idx_historical_max_rankings_stock ON historical_max_rankings (stock_code)'),
                ('idx_historical_max_rankings_is_max', 'CREATE INDEX idx_historical_max_rankings_is_max ON historical_max_rankings (is_historical_max)')
            ]
        },
        'stock_historical_max_rankings': {
            'create_table': '''
                CREATE TABLE stock_historical_max_rankings (
                    stock_code TEXT,
                    year TEXT,
                    metric TEXT,
                    rank_position INTEGER,
                    value REAL,
                    date TEXT,
                    PRIMARY KEY (stock_code, year, metric, rank_position)
                )
            ''',
            'indexes': [
                ('idx_stock_historical_max_rankings_stock_year', 'CREATE INDEX idx_stock_historical_max_rankings_stock_year ON stock_historical_max_rankings (stock_code, year)'),
                ('idx_stock_historical_max_rankings_metric', 'CREATE INDEX idx_stock_historical_max_rankings_metric ON stock_historical_max_rankings (metric)'),
                ('idx_stock_historical_max_rankings_year', 'CREATE INDEX idx_stock_historical_max_rankings_year ON stock_historical_max_rankings (year)')
            ]
        }
    }

    # 创建缺失的表和索引
    for table_name, table_info in tables.items():
        if table_name not in existing_tables:
            logging.info(f"创建缺失表: {table_name}")
            cursor.execute(table_info['create_table'])
            for index_name, index_sql in table_info['indexes']:
                cursor.execute(index_sql)
                logging.info(f"创建索引: {index_name} for {table_name}")

    # 检查并添加缺失的字段
    # 检查stock_metadata表是否有last_ranking_update字段
    if 'stock_metadata' in existing_tables:
        cursor.execute("PRAGMA table_info(stock_metadata)")
        columns = [column[1] for column in cursor.fetchall()]
        if 'last_ranking_update' not in columns:
            logging.info("添加缺失字段: stock_metadata.last_ranking_update")
            cursor.execute("ALTER TABLE stock_metadata ADD COLUMN last_ranking_update TEXT")

    conn.commit()
    conn.close()


# 分钟级数据库初始化
def init_minute_database():
    """初始化分钟级数据库"""
    conn = sqlite3.connect(DB_NAME_MINUTE)
    cursor = conn.cursor()

    # 查询现有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    existing_tables = {row[0] for row in cursor.fetchall()}

    # 分钟级数据表定义
    tables = {
        'minute_fund_flow': {
            'create_table': '''
                CREATE TABLE minute_fund_flow (
                    stock_code TEXT,
                    datetime TEXT,
                    date TEXT,
                    time TEXT,
                    main_net_inflow REAL,
                    main_net_ratio REAL,
                    super_large_net_inflow REAL,
                    super_large_net_ratio REAL,
                    large_net_inflow REAL,
                    large_net_ratio REAL,
                    medium_net_inflow REAL,
                    medium_net_ratio REAL,
                    small_net_inflow REAL,
                    small_net_ratio REAL,
                    PRIMARY KEY (stock_code, datetime)
                )
            ''',
            'indexes': [
                ('idx_minute_fund_flow_stock_datetime', 'CREATE INDEX idx_minute_fund_flow_stock_datetime ON minute_fund_flow (stock_code, datetime)'),
                ('idx_minute_fund_flow_date', 'CREATE INDEX idx_minute_fund_flow_date ON minute_fund_flow (date)'),
                ('idx_minute_fund_flow_stock_date', 'CREATE INDEX idx_minute_fund_flow_stock_date ON minute_fund_flow (stock_code, date)')
            ]
        },
        'minute_metadata': {
            'create_table': '''
                CREATE TABLE minute_metadata (
                    stock_code TEXT PRIMARY KEY,
                    last_minute_update_date TEXT,
                    last_minute_update_time TEXT
                )
            ''',
            'indexes': []
        }
    }

    # 创建缺失的表和索引
    for table_name, table_info in tables.items():
        if table_name not in existing_tables:
            logging.info(f"创建分钟级数据表: {table_name}")
            cursor.execute(table_info['create_table'])
            for index_name, index_sql in table_info['indexes']:
                cursor.execute(index_sql)
                logging.info(f"创建索引: {index_name} for {table_name}")

    conn.commit()
    conn.close()


# 获取所有股票代码
def get_all_stocks():
    try:
        try:
            stocks = ak.stock_info_a_code_name()
            valid_codes = stocks[
                stocks['code'].str.match(r'^\d{6}$') &
                (stocks['code'].str.startswith(('6', '0', '3')))
                ]['code'].tolist()
        except Exception:
            logging.info("主接口获取失败，尝试备用接口 stock_zh_a_spot_em")
            stocks = ak.stock_zh_a_spot_em()
            valid_codes = stocks[
                stocks['代码'].str.match(r'^\d{6}$') &
                (stocks['代码'].str.startswith(('6', '0', '3')))
                ]['代码'].tolist()

        stock_list = []
        for code in set(valid_codes):
            market = 'sh' if code.startswith('6') else 'sz'
            stock_list.append((code, market))

        logging.info(f"获取到 {len(stock_list)} 只股票")
        return stock_list
    except Exception as e:
        logging.error(f"获取股票列表失败: {e}")
        return []


# 检查数据库中最新日期
def get_latest_date(stock):
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute('''
        SELECT MAX(date) FROM daily_fund_flow WHERE stock_code = ?
    ''', (stock,))
    latest_date = cursor.fetchone()[0]
    conn.close()
    return latest_date


# 检查分钟级数据库中最新日期
def get_latest_minute_date(stock):
    """获取股票分钟级数据的最新日期"""
    conn = sqlite3.connect(DB_NAME_MINUTE)
    cursor = conn.cursor()
    cursor.execute('''
        SELECT last_minute_update_date FROM minute_metadata WHERE stock_code = ?
    ''', (stock,))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else None


# 检查是否需要更新分钟级数据
def need_minute_update(stock):
    """检查是否需要更新分钟级数据"""
    latest_minute_date = get_latest_minute_date(stock)
    today = datetime.now().strftime('%Y-%m-%d')

    # 检查分钟级数据库是否为空或者今天最新的数据没有
    if latest_minute_date is None:
        # 没有数据，需要更新
        return True

    if latest_minute_date < today:
        # 数据不是今天的，需要更新
        return True

    # 额外检查：如果fund_flow.db为空或者今天最新的数据没有，也需要更新分钟级数据
    try:
        conn = sqlite3.connect(DB_NAME)
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM daily_fund_flow WHERE stock_code = ? AND date = ?', (stock, today))
        daily_count = cursor.fetchone()[0]
        conn.close()

        if daily_count == 0:
            # 今天没有日级数据，可能需要分钟级数据来补充
            return True
    except Exception as e:
        logging.warning(f"检查日级数据时出错: {e}")

    return False


# 获取分钟级资金流向数据
def fetch_minute_fund_flow(stock):
    """获取单个股票的分钟级最新交易日数据"""
    if not re.match(r'^\d{6}$', stock):
        logging.error(f"无效股票代码: {stock}")
        return None

    max_retries = 3
    for attempt in range(max_retries):
        try:
            # 使用efinance获取分钟级数据
            df = ef.stock.get_today_bill(stock)

            if df is None or df.empty:
                return None

            # 确保时间列为 datetime 类型并按时间排序
            df['时间'] = pd.to_datetime(df['时间'])
            df = df.sort_values('时间', ascending=True)

            # 添加股票代码和处理时间字段
            df['stock_code'] = stock
            df['datetime'] = df['时间'].dt.strftime('%Y-%m-%d %H:%M:%S')
            df['date'] = df['时间'].dt.strftime('%Y-%m-%d')
            df['time'] = df['时间'].dt.strftime('%H:%M:%S')

            logging.info(f"获取到 {stock} 分钟级数据 {len(df)} 条记录，时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
            return df

        except (ValueError, TypeError) as e:
            # JSON解析错误或数据类型错误，通常表示没有数据
            if "Expecting value" in str(e) or "NoneType" in str(e):
                return None
            logging.warning(f"获取 {stock} 分钟级数据失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(1)
            continue
        except Exception as e:
            logging.warning(f"获取 {stock} 分钟级数据失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(1)
            continue

    return None


# 保存分钟级数据到数据库
def save_minute_data_to_db(df, stock):
    """保存分钟级数据到数据库"""
    if df is None or df.empty:
        return

    conn = sqlite3.connect(DB_NAME_MINUTE)
    cursor = conn.cursor()

    try:
        # 保存分钟级数据
        for _, row in df.iterrows():
            cursor.execute('''
                INSERT OR REPLACE INTO minute_fund_flow (
                    stock_code, datetime, date, time,
                    main_net_inflow, main_net_ratio,
                    super_large_net_inflow, super_large_net_ratio,
                    large_net_inflow, large_net_ratio,
                    medium_net_inflow, medium_net_ratio,
                    small_net_inflow, small_net_ratio
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                stock, row['datetime'], row['date'], row['time'],
                row['主力净流入'], 0,  # efinance没有净占比数据，设为0
                row['超大单净流入'], 0,
                row['大单净流入'], 0,
                row['中单净流入'], 0,
                row['小单净流入'], 0
            ))

        # 更新元数据
        latest_date = df['date'].max()
        latest_time = df['time'].max()
        cursor.execute('''
            INSERT OR REPLACE INTO minute_metadata
            (stock_code, last_minute_update_date, last_minute_update_time)
            VALUES (?, ?, ?)
        ''', (stock, latest_date, latest_time))

        conn.commit()
        logging.info(f"已保存 {stock} 的分钟级数据 {len(df)} 条记录到数据库")

    except Exception as e:
        logging.error(f"保存 {stock} 分钟级数据失败: {e}")
        conn.rollback()
    finally:
        conn.close()


# 获取资金流向数据（增量更新）
# 获取资金流向数据（增量更新）
def fetch_fund_flow(stock, market, latest_date):
    if not re.match(r'^\d{6}$', stock):
        logging.error(f"无效股票代码: {stock}")
        return None

    max_retries = 3
    for attempt in range(max_retries):
        try:
            df = ak.stock_individual_fund_flow(stock=stock, market=market)
            df['stock_code'] = stock
            df['date'] = pd.to_datetime(df['日期']).dt.strftime('%Y-%m-%d')
            if latest_date:
                df = df[pd.to_datetime(df['date']) > pd.to_datetime(latest_date)]
            return df
        except (ConnectionError, Timeout) as e:
            logging.warning(f"获取 {stock} 资金流向失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
            continue
        except Exception as e:
            logging.error(f"获取 {stock} 资金流向失败: {e}")
            break
    return None


# 保存数据到数据库
def save_to_db(df, stock, rank_type=None):
    if df is None or df.empty:
        return
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    if rank_type:
        for _, row in df.iterrows():
            cursor.execute('''
                INSERT OR REPLACE INTO rank_data (
                    stock_code, rank_type, date, main_net_inflow, main_net_ratio,
                    super_large_net_inflow, super_large_net_ratio, large_net_inflow, large_net_ratio,
                    medium_net_inflow, medium_net_ratio, small_net_inflow, small_net_ratio
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                stock, rank_type, row['date'], row['主力净流入-净额'], row['主力净流入-净占比'],
                row['超大单净流入-净额'], row['超大单净流入-净占比'], row['大单净流入-净额'], row['大单净流入-净占比'],
                row['中单净流入-净额'], row['中单净流入-净占比'], row['小单净流入-净额'], row['小单净流入-净占比']
            ))
    else:
        for _, row in df.iterrows():
            cursor.execute('''
                INSERT OR REPLACE INTO daily_fund_flow (
                    stock_code, date, close_price, change_pct, main_net_inflow, main_net_ratio,
                    super_large_net_inflow, super_large_net_ratio, large_net_inflow, large_net_ratio,
                    medium_net_inflow, medium_net_ratio, small_net_inflow, small_net_ratio
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                stock, row['date'], row['收盘价'], row['涨跌幅'], row['主力净流入-净额'], row['主力净流入-净占比'],
                row['超大单净流入-净额'], row['超大单净流入-净占比'], row['大单净流入-净额'], row['大单净流入-净占比'],
                row['中单净流入-净额'], row['中单净流入-净占比'], row['小单净流入-净额'], row['小单净流入-净占比']
            ))

        latest_date = df['date'].max()
        cursor.execute('''
            INSERT OR REPLACE INTO stock_metadata (stock_code, last_data_update)
            VALUES (?, ?)
        ''', (stock, latest_date))

    conn.commit()
    conn.close()


# 计算上涨/下跌股票的平均资金流入指标并打印
def calculate_stats():
    conn = sqlite3.connect(DB_NAME)
    df = pd.read_sql_query("SELECT stock_code, change_pct, main_net_inflow, main_net_ratio, super_large_net_inflow, super_large_net_ratio, large_net_inflow, large_net_ratio, medium_net_inflow, medium_net_ratio, small_net_inflow, small_net_ratio FROM daily_fund_flow", conn)

    if df.empty:
        logging.warning("daily_fund_flow 表中没有数据，无法计算统计指标。")
        conn.close()
        return None, None

    cursor = conn.cursor()
    cursor.execute("""
        SELECT stock_code FROM stock_metadata
        WHERE last_stats_update IS NULL OR last_data_update > last_stats_update
    """)
    stocks_to_compute = [row[0] for row in cursor.fetchall()]

    if stocks_to_compute:
        logging.info(f"[1/3] 检测到 {len(stocks_to_compute)} 只股票需要更新统计数据。")
        df = df[df['stock_code'].isin(stocks_to_compute)]
    else:
        logging.info("[1/3] 所有股票的涨跌平均统计已是最新。")
        conn.close()
        return None, None

    stock_stats_up = []
    stock_stats_down = []
    today = datetime.now().strftime('%Y-%m-%d')

    for stock_code, group in tqdm(df.groupby('stock_code'), desc="[1/3] 正在计算涨跌平均统计..."):
        stock_up_df = group[group['change_pct'] > 0]
        if not stock_up_df.empty:
            stock_up_stats = {
                'stock_code': stock_code,
                'date': today,
                'avg_main_net_inflow': stock_up_df['main_net_inflow'].mean(),
                'avg_main_net_ratio': stock_up_df['main_net_ratio'].mean(),
                'avg_super_large_net_inflow': stock_up_df['super_large_net_inflow'].mean(),
                'avg_super_large_net_ratio': stock_up_df['super_large_net_ratio'].mean(),
                'avg_large_net_inflow': stock_up_df['large_net_inflow'].mean(),
                'avg_large_net_ratio': stock_up_df['large_net_ratio'].mean(),
                'avg_medium_net_inflow': stock_up_df['medium_net_inflow'].mean(),
                'avg_medium_net_ratio': stock_up_df['medium_net_ratio'].mean(),
                'avg_small_net_inflow': stock_up_df['small_net_inflow'].mean(),
                'avg_small_net_ratio': stock_up_df['small_net_ratio'].mean(),
                'record_count': len(stock_up_df)
            }
            stock_stats_up.append(stock_up_stats)

        stock_down_df = group[group['change_pct'] < 0]
        if not stock_down_df.empty:
            stock_down_stats = {
                'stock_code': stock_code,
                'date': today,
                'avg_main_net_inflow': stock_down_df['main_net_inflow'].mean(),
                'avg_main_net_ratio': stock_down_df['main_net_ratio'].mean(),
                'avg_super_large_net_inflow': stock_down_df['super_large_net_inflow'].mean(),
                'avg_super_large_net_ratio': stock_down_df['super_large_net_ratio'].mean(),
                'avg_large_net_inflow': stock_down_df['large_net_inflow'].mean(),
                'avg_large_net_ratio': stock_down_df['large_net_ratio'].mean(),
                'avg_medium_net_inflow': stock_down_df['medium_net_inflow'].mean(),
                'avg_medium_net_ratio': stock_down_df['medium_net_ratio'].mean(),
                'avg_small_net_inflow': stock_down_df['small_net_inflow'].mean(),
                'avg_small_net_ratio': stock_down_df['small_net_ratio'].mean(),
                'record_count': len(stock_down_df)
            }
            stock_stats_down.append(stock_down_stats)

    if stock_stats_up:
        pd.DataFrame(stock_stats_up).to_sql('stock_stats_up', conn, if_exists='replace', index=False)
    if stock_stats_down:
        pd.DataFrame(stock_stats_down).to_sql('stock_stats_down', conn, if_exists='replace', index=False)

    if stock_stats_up or stock_stats_down:
        cursor = conn.cursor()
        all_stock_codes = df['stock_code'].unique()
        update_data = [(today, stock_code) for stock_code in all_stock_codes]
        cursor.executemany('''
            UPDATE stock_metadata SET last_stats_update = ? WHERE stock_code = ?
        ''', update_data)
        conn.commit()

    conn.close()
    return None, None


# 计算特定涨幅/跌幅所需的平均资金净额并打印
def calculate_threshold_stats():
    conn = sqlite3.connect(DB_NAME)
    df = pd.read_sql_query(
        "SELECT stock_code, change_pct, main_net_inflow, super_large_net_inflow, large_net_inflow FROM daily_fund_flow",
        conn)

    if df.empty:
        logging.warning("daily_fund_flow 表中没有数据，无法计算阈值统计。")
        conn.close()
        return None

    cursor = conn.cursor()
    cursor.execute("""
        SELECT stock_code FROM stock_metadata
        WHERE last_stats_update IS NULL OR last_data_update > last_stats_update
    """)
    stocks_to_compute = [row[0] for row in cursor.fetchall()]

    if stocks_to_compute:
        logging.info(f"[2/3] 检测到 {len(stocks_to_compute)} 只股票需要更新阈值统计。")
        df = df[df['stock_code'].isin(stocks_to_compute)]
    else:
        logging.info("[2/3] 所有股票的涨跌阈值统计已是最新。")
        conn.close()
        return None

    stock_threshold_stats = []
    today = datetime.now().strftime('%Y-%m-%d')

    for stock_code, group in tqdm(df.groupby('stock_code'), desc="[2/3] 正在计算涨跌阈值统计..."):
        for threshold in [10, 8, 5, 3]:
            stock_up_df = group[group['change_pct'] >= threshold]
            if not stock_up_df.empty:
                stock_up_threshold = {
                    'stock_code': stock_code,
                    'threshold_type': f'up_{threshold}',
                    'avg_main_net_inflow': stock_up_df['main_net_inflow'].mean(),
                    'avg_super_large_net_inflow': stock_up_df['super_large_net_inflow'].mean(),
                    'avg_large_net_inflow': stock_up_df['large_net_inflow'].mean(),
                    'record_count': len(stock_up_df)
                }
                stock_threshold_stats.append(stock_up_threshold)

        for threshold in [9.9, 8, 5, 3]:
            stock_down_df = group[group['change_pct'] <= -threshold]
            if not stock_down_df.empty:
                stock_down_threshold = {
                    'stock_code': stock_code,
                    'threshold_type': f'down_{threshold}',
                    'avg_main_net_inflow': stock_down_df['main_net_inflow'].mean(),
                    'avg_super_large_net_inflow': stock_down_df['super_large_net_inflow'].mean(),
                    'avg_large_net_inflow': stock_down_df['large_net_inflow'].mean(),
                    'record_count': len(stock_down_df)
                }
                stock_threshold_stats.append(stock_down_threshold)

    if stock_threshold_stats:
        pd.DataFrame(stock_threshold_stats).to_sql('stock_stats_threshold', conn, if_exists='replace', index=False)

    conn.close()
    return None


# 计算最大/最小值并打印
def calculate_extremes():
    conn = sqlite3.connect(DB_NAME)
    df = pd.read_sql_query("SELECT * FROM daily_fund_flow", conn)

    if df.empty:
        logging.warning("daily_fund_flow 表中没有数据，无法计算极值。")
        conn.close()
        return None

    cursor = conn.cursor()
    cursor.execute("""
        SELECT stock_code FROM stock_metadata
        WHERE last_stats_update IS NULL OR last_data_update > last_stats_update
    """)
    stocks_to_compute = [row[0] for row in cursor.fetchall()]

    if stocks_to_compute:
        logging.info(f"[3/3] 检测到 {len(stocks_to_compute)} 只股票需要更新历史极值。")
        df = df[df['stock_code'].isin(stocks_to_compute)]
    else:
        logging.info("[3/3] 所有股票的历史极值已是最新。")
        conn.close()
        return None

    stock_extremes = []

    metrics = [
        ('main_net_inflow', '主力净流入净额'),
        ('main_net_ratio', '主力净占比'),
        ('super_large_net_inflow', '超大单净流入净额'),
        ('super_large_net_ratio', '超大单净占比'),
        ('large_net_inflow', '大单净流入净额'),
        ('large_net_ratio', '大单净占比'),
        ('medium_net_inflow', '中单净流入净额'),
        ('medium_net_ratio', '中单净占比'),
        ('small_net_inflow', '小单净流入净额'),
        ('small_net_ratio', '小单净占比')
    ]

    for stock_code, group in tqdm(df.groupby('stock_code'), desc="[3/3] 正在计算个股历史极值..."):
        for metric, metric_name in metrics:
            if not group[metric].isna().all():
                stock_max_row = group.loc[group[metric].idxmax()]
                stock_min_row = group.loc[group[metric].idxmin()]

                stock_extreme = {
                    'stock_code': stock_code,
                    'metric': metric,
                    'max_value': stock_max_row[metric],
                    'max_date': stock_max_row['date'],
                    'min_value': stock_min_row[metric],
                    'min_date': stock_min_row['date']
                }
                stock_extremes.append(stock_extreme)

    if stock_extremes:
        pd.DataFrame(stock_extremes).to_sql('stock_stats_extremes', conn, if_exists='replace', index=False)

    conn.close()
    return None


# 按年份计算指标的新函数
def calculate_yearly_indicators(stock_codes=None):
    """按年份计算指标"""
    logging.info("开始按年份计算指标...")

    conn = sqlite3.connect(DB_NAME)

    # 如果没有指定股票，获取所有股票
    if stock_codes is None:
        cursor = conn.cursor()
        cursor.execute("SELECT DISTINCT stock_code FROM daily_fund_flow")
        stock_codes = [row[0] for row in cursor.fetchall()]

    if not stock_codes:
        logging.warning("没有找到股票数据")
        conn.close()
        return

    logging.info(f"开始为 {len(stock_codes)} 只股票按年份计算指标...")

    for stock_code in tqdm(stock_codes, desc="按年份计算指标"):
        calculate_yearly_stats_for_stock(stock_code, conn)
        calculate_yearly_threshold_stats_for_stock(stock_code, conn)
        calculate_yearly_extremes_for_stock(stock_code, conn)

    conn.close()
    logging.info("按年份计算指标完成")


def calculate_yearly_stats_for_stock(stock_code, conn):
    """为单只股票按年份计算涨跌统计"""
    cursor = conn.cursor()

    # 获取该股票的所有数据
    df = pd.read_sql_query("""
        SELECT date, change_pct, main_net_inflow, main_net_ratio,
               super_large_net_inflow, super_large_net_ratio,
               large_net_inflow, large_net_ratio,
               medium_net_inflow, medium_net_ratio,
               small_net_inflow, small_net_ratio
        FROM daily_fund_flow
        WHERE stock_code = ? AND change_pct IS NOT NULL
        ORDER BY date
    """, conn, params=[stock_code])

    if df.empty:
        return

    # 添加年份列
    df['year'] = pd.to_datetime(df['date']).dt.year

    yearly_stats = []

    # 按年份分组计算
    for year, year_data in df.groupby('year'):
        year_str = str(year)

        # 上涨数据
        up_data = year_data[year_data['change_pct'] > 0]
        if not up_data.empty:
            yearly_stats.append({
                'stock_code': stock_code,
                'year': year_str,
                'period_type': 'up',
                'avg_main_net_inflow': up_data['main_net_inflow'].mean(),
                'avg_main_net_ratio': up_data['main_net_ratio'].mean(),
                'avg_super_large_net_inflow': up_data['super_large_net_inflow'].mean(),
                'avg_super_large_net_ratio': up_data['super_large_net_ratio'].mean(),
                'avg_large_net_inflow': up_data['large_net_inflow'].mean(),
                'avg_large_net_ratio': up_data['large_net_ratio'].mean(),
                'avg_medium_net_inflow': up_data['medium_net_inflow'].mean(),
                'avg_medium_net_ratio': up_data['medium_net_ratio'].mean(),
                'avg_small_net_inflow': up_data['small_net_inflow'].mean(),
                'avg_small_net_ratio': up_data['small_net_ratio'].mean(),
                'record_count': len(up_data)
            })

        # 下跌数据
        down_data = year_data[year_data['change_pct'] < 0]
        if not down_data.empty:
            yearly_stats.append({
                'stock_code': stock_code,
                'year': year_str,
                'period_type': 'down',
                'avg_main_net_inflow': down_data['main_net_inflow'].mean(),
                'avg_main_net_ratio': down_data['main_net_ratio'].mean(),
                'avg_super_large_net_inflow': down_data['super_large_net_inflow'].mean(),
                'avg_super_large_net_ratio': down_data['super_large_net_ratio'].mean(),
                'avg_large_net_inflow': down_data['large_net_inflow'].mean(),
                'avg_large_net_ratio': down_data['large_net_ratio'].mean(),
                'avg_medium_net_inflow': down_data['medium_net_inflow'].mean(),
                'avg_medium_net_ratio': down_data['medium_net_ratio'].mean(),
                'avg_small_net_inflow': down_data['small_net_inflow'].mean(),
                'avg_small_net_ratio': down_data['small_net_ratio'].mean(),
                'record_count': len(down_data)
            })

    # 计算总的历史数据（所有年份）
    if not df.empty:
        # 总的上涨数据
        total_up_data = df[df['change_pct'] > 0]
        if not total_up_data.empty:
            yearly_stats.append({
                'stock_code': stock_code,
                'year': 'total',
                'period_type': 'up',
                'avg_main_net_inflow': total_up_data['main_net_inflow'].mean(),
                'avg_main_net_ratio': total_up_data['main_net_ratio'].mean(),
                'avg_super_large_net_inflow': total_up_data['super_large_net_inflow'].mean(),
                'avg_super_large_net_ratio': total_up_data['super_large_net_ratio'].mean(),
                'avg_large_net_inflow': total_up_data['large_net_inflow'].mean(),
                'avg_large_net_ratio': total_up_data['large_net_ratio'].mean(),
                'avg_medium_net_inflow': total_up_data['medium_net_inflow'].mean(),
                'avg_medium_net_ratio': total_up_data['medium_net_ratio'].mean(),
                'avg_small_net_inflow': total_up_data['small_net_inflow'].mean(),
                'avg_small_net_ratio': total_up_data['small_net_ratio'].mean(),
                'record_count': len(total_up_data)
            })

        # 总的下跌数据
        total_down_data = df[df['change_pct'] < 0]
        if not total_down_data.empty:
            yearly_stats.append({
                'stock_code': stock_code,
                'year': 'total',
                'period_type': 'down',
                'avg_main_net_inflow': total_down_data['main_net_inflow'].mean(),
                'avg_main_net_ratio': total_down_data['main_net_ratio'].mean(),
                'avg_super_large_net_inflow': total_down_data['super_large_net_inflow'].mean(),
                'avg_super_large_net_ratio': total_down_data['super_large_net_ratio'].mean(),
                'avg_large_net_inflow': total_down_data['large_net_inflow'].mean(),
                'avg_large_net_ratio': total_down_data['large_net_ratio'].mean(),
                'avg_medium_net_inflow': total_down_data['medium_net_inflow'].mean(),
                'avg_medium_net_ratio': total_down_data['medium_net_ratio'].mean(),
                'avg_small_net_inflow': total_down_data['small_net_inflow'].mean(),
                'avg_small_net_ratio': total_down_data['small_net_ratio'].mean(),
                'record_count': len(total_down_data)
            })

    # 删除该股票的旧数据并保存新数据
    if yearly_stats:
        cursor.execute("DELETE FROM stock_stats_yearly WHERE stock_code = ?", (stock_code,))
        for stats in yearly_stats:
            cursor.execute('''
                INSERT INTO stock_stats_yearly
                (stock_code, year, period_type, avg_main_net_inflow, avg_main_net_ratio,
                 avg_super_large_net_inflow, avg_super_large_net_ratio,
                 avg_large_net_inflow, avg_large_net_ratio,
                 avg_medium_net_inflow, avg_medium_net_ratio,
                 avg_small_net_inflow, avg_small_net_ratio, record_count)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                stats['stock_code'], stats['year'], stats['period_type'],
                stats['avg_main_net_inflow'], stats['avg_main_net_ratio'],
                stats['avg_super_large_net_inflow'], stats['avg_super_large_net_ratio'],
                stats['avg_large_net_inflow'], stats['avg_large_net_ratio'],
                stats['avg_medium_net_inflow'], stats['avg_medium_net_ratio'],
                stats['avg_small_net_inflow'], stats['avg_small_net_ratio'],
                stats['record_count']
            ))
        conn.commit()


def calculate_yearly_threshold_stats_for_stock(stock_code, conn):
    """为单只股票按年份计算阈值统计"""
    cursor = conn.cursor()

    # 获取该股票的数据
    df = pd.read_sql_query("""
        SELECT date, change_pct, main_net_inflow, super_large_net_inflow, large_net_inflow
        FROM daily_fund_flow
        WHERE stock_code = ? AND change_pct IS NOT NULL
        ORDER BY date
    """, conn, params=[stock_code])

    if df.empty:
        return

    # 添加年份列
    df['year'] = pd.to_datetime(df['date']).dt.year

    yearly_threshold_stats = []

    # 按年份分组计算
    for year, year_data in df.groupby('year'):
        year_str = str(year)

        # 涨幅阈值
        for threshold in [10, 8, 5, 3]:
            threshold_data = year_data[year_data['change_pct'] >= threshold]
            if not threshold_data.empty:
                yearly_threshold_stats.append({
                    'stock_code': stock_code,
                    'year': year_str,
                    'threshold_type': f'up_{threshold}',
                    'avg_main_net_inflow': threshold_data['main_net_inflow'].mean(),
                    'avg_super_large_net_inflow': threshold_data['super_large_net_inflow'].mean(),
                    'avg_large_net_inflow': threshold_data['large_net_inflow'].mean(),
                    'record_count': len(threshold_data)
                })

        # 跌幅阈值
        for threshold in [9.9, 8, 5, 3]:
            threshold_data = year_data[year_data['change_pct'] <= -threshold]
            if not threshold_data.empty:
                yearly_threshold_stats.append({
                    'stock_code': stock_code,
                    'year': year_str,
                    'threshold_type': f'down_{threshold}',
                    'avg_main_net_inflow': threshold_data['main_net_inflow'].mean(),
                    'avg_super_large_net_inflow': threshold_data['super_large_net_inflow'].mean(),
                    'avg_large_net_inflow': threshold_data['large_net_inflow'].mean(),
                    'record_count': len(threshold_data)
                })

    # 计算总的历史数据（所有年份）
    if not df.empty:
        # 总的涨幅阈值
        for threshold in [10, 8, 5, 3]:
            threshold_data = df[df['change_pct'] >= threshold]
            if not threshold_data.empty:
                yearly_threshold_stats.append({
                    'stock_code': stock_code,
                    'year': 'total',
                    'threshold_type': f'up_{threshold}',
                    'avg_main_net_inflow': threshold_data['main_net_inflow'].mean(),
                    'avg_super_large_net_inflow': threshold_data['super_large_net_inflow'].mean(),
                    'avg_large_net_inflow': threshold_data['large_net_inflow'].mean(),
                    'record_count': len(threshold_data)
                })

        # 总的跌幅阈值
        for threshold in [9.9, 8, 5, 3]:
            threshold_data = df[df['change_pct'] <= -threshold]
            if not threshold_data.empty:
                yearly_threshold_stats.append({
                    'stock_code': stock_code,
                    'year': 'total',
                    'threshold_type': f'down_{threshold}',
                    'avg_main_net_inflow': threshold_data['main_net_inflow'].mean(),
                    'avg_super_large_net_inflow': threshold_data['super_large_net_inflow'].mean(),
                    'avg_large_net_inflow': threshold_data['large_net_inflow'].mean(),
                    'record_count': len(threshold_data)
                })

    # 删除该股票的旧数据并保存新数据
    if yearly_threshold_stats:
        cursor.execute("DELETE FROM stock_stats_threshold_yearly WHERE stock_code = ?", (stock_code,))
        for stats in yearly_threshold_stats:
            cursor.execute('''
                INSERT INTO stock_stats_threshold_yearly
                (stock_code, year, threshold_type, avg_main_net_inflow,
                 avg_super_large_net_inflow, avg_large_net_inflow, record_count)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                stats['stock_code'], stats['year'], stats['threshold_type'],
                stats['avg_main_net_inflow'], stats['avg_super_large_net_inflow'],
                stats['avg_large_net_inflow'], stats['record_count']
            ))
        conn.commit()


def calculate_yearly_extremes_for_stock(stock_code, conn):
    """为单只股票按年份计算极值统计"""
    cursor = conn.cursor()

    # 获取该股票的所有数据
    df = pd.read_sql_query("SELECT * FROM daily_fund_flow WHERE stock_code = ?", conn, params=[stock_code])

    if df.empty:
        return

    # 添加年份列
    df['year'] = pd.to_datetime(df['date']).dt.year

    yearly_extremes = []

    metrics = [
        ('main_net_inflow', '主力净流入净额'),
        ('main_net_ratio', '主力净占比'),
        ('super_large_net_inflow', '超大单净流入净额'),
        ('super_large_net_ratio', '超大单净占比'),
        ('large_net_inflow', '大单净流入净额'),
        ('large_net_ratio', '大单净占比'),
        ('medium_net_inflow', '中单净流入净额'),
        ('medium_net_ratio', '中单净占比'),
        ('small_net_inflow', '小单净流入净额'),
        ('small_net_ratio', '小单净占比')
    ]

    # 按年份分组计算
    for year, year_data in df.groupby('year'):
        year_str = str(year)

        for metric, _ in metrics:
            if not year_data[metric].isna().all():
                max_row = year_data.loc[year_data[metric].idxmax()]
                min_row = year_data.loc[year_data[metric].idxmin()]

                yearly_extremes.append({
                    'stock_code': stock_code,
                    'year': year_str,
                    'metric': metric,
                    'max_value': max_row[metric],
                    'max_date': max_row['date'],
                    'min_value': min_row[metric],
                    'min_date': min_row['date']
                })

    # 计算总的历史数据（所有年份）
    if not df.empty:
        for metric, _ in metrics:
            if not df[metric].isna().all():
                max_row = df.loc[df[metric].idxmax()]
                min_row = df.loc[df[metric].idxmin()]

                yearly_extremes.append({
                    'stock_code': stock_code,
                    'year': 'total',
                    'metric': metric,
                    'max_value': max_row[metric],
                    'max_date': max_row['date'],
                    'min_value': min_row[metric],
                    'min_date': min_row['date']
                })

    # 删除该股票的旧数据并保存新数据
    if yearly_extremes:
        cursor.execute("DELETE FROM stock_stats_extremes_yearly WHERE stock_code = ?", (stock_code,))
        for extreme in yearly_extremes:
            cursor.execute('''
                INSERT INTO stock_stats_extremes_yearly
                (stock_code, year, metric, max_value, max_date, min_value, min_date)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                extreme['stock_code'], extreme['year'], extreme['metric'],
                extreme['max_value'], extreme['max_date'],
                extreme['min_value'], extreme['min_date']
            ))
        conn.commit()


def get_latest_ranking_date():
    """获取最新排行榜日期"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute("SELECT MAX(date) FROM daily_rankings")
    latest_date = cursor.fetchone()[0]
    conn.close()
    return latest_date


def generate_daily_rankings_incremental():
    """增量生成每日排行榜"""
    logging.info("开始增量生成每日排行榜...")

    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    try:
        # 获取最新排行榜日期
        latest_ranking_date = get_latest_ranking_date()

        # 获取需要生成排行榜的日期
        if latest_ranking_date:
            cursor.execute('''
                SELECT DISTINCT date FROM daily_fund_flow
                WHERE date > ?
                ORDER BY date
            ''', (latest_ranking_date,))
        else:
            cursor.execute('''
                SELECT DISTINCT date FROM daily_fund_flow
                ORDER BY date
            ''')

        dates_to_process = [row[0] for row in cursor.fetchall()]

        if not dates_to_process:
            logging.info("没有需要生成排行榜的新日期")
            return

        logging.info(f"需要生成排行榜的日期: {len(dates_to_process)} 个")

        # 为每个日期生成排行榜
        for date in tqdm(dates_to_process, desc="生成增量排行榜"):
            generate_ranking_for_date(date, cursor)

        conn.commit()
        logging.info(f"增量排行榜生成完成，处理了 {len(dates_to_process)} 个日期")

    except Exception as e:
        logging.error(f"增量生成排行榜时出错: {e}")
        conn.rollback()
    finally:
        conn.close()


def generate_daily_rankings_full():
    """生成全部时间范围的每日排行榜"""
    logging.info("开始生成全部时间范围的每日排行榜...")

    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    try:
        # 清空现有排行榜数据
        cursor.execute("DELETE FROM daily_rankings")

        # 获取所有日期
        cursor.execute('''
            SELECT DISTINCT date FROM daily_fund_flow
            ORDER BY date
        ''')

        dates_to_process = [row[0] for row in cursor.fetchall()]

        if not dates_to_process:
            logging.info("没有数据可以生成排行榜")
            return

        logging.info(f"需要生成排行榜的日期: {len(dates_to_process)} 个")

        # 为每个日期生成排行榜
        for date in tqdm(dates_to_process, desc="生成全量排行榜"):
            generate_ranking_for_date(date, cursor)

        conn.commit()
        logging.info(f"全量排行榜生成完成，处理了 {len(dates_to_process)} 个日期")

    except Exception as e:
        logging.error(f"生成全量排行榜时出错: {e}")
        conn.rollback()
    finally:
        conn.close()


# 获取历史最大值排行榜最新日期
def get_latest_historical_max_ranking_date():
    """获取历史最大值排行榜最新日期"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute("SELECT MAX(date) FROM historical_max_rankings")
    latest_date = cursor.fetchone()[0]
    conn.close()
    return latest_date


# 生成历史最大值排行榜（增量更新）
def generate_historical_max_rankings_incremental():
    """生成历史最大值排行榜（增量更新）"""
    logging.info("开始增量生成历史最大值排行榜...")

    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    try:
        # 获取最新历史最大值排行榜日期
        latest_historical_max_date = get_latest_historical_max_ranking_date()

        # 获取需要生成历史最大值排行榜的日期
        if latest_historical_max_date:
            if DATA_TYPE == 'all':
                cursor.execute('''
                    SELECT DISTINCT date FROM daily_fund_flow
                    WHERE date > ?
                    ORDER BY date
                ''', (latest_historical_max_date,))
            else:  # single
                cursor.execute('''
                    SELECT DISTINCT date FROM daily_fund_flow
                    WHERE stock_code = ? AND date > ?
                    ORDER BY date
                ''', (SINGLE_STOCK, latest_historical_max_date))
        else:
            if DATA_TYPE == 'all':
                cursor.execute('''
                    SELECT DISTINCT date FROM daily_fund_flow
                    ORDER BY date
                ''')
            else:  # single
                cursor.execute('''
                    SELECT DISTINCT date FROM daily_fund_flow
                    WHERE stock_code = ?
                    ORDER BY date
                ''', (SINGLE_STOCK,))

        dates_to_process = [row[0] for row in cursor.fetchall()]

        if not dates_to_process:
            logging.info("没有需要生成历史最大值排行榜的新日期")
            return

        # 历史最大值排行榜的指标
        historical_max_metrics = [
            'main_net_inflow',
            'main_net_ratio',
            'super_large_net_inflow',
            'super_large_net_ratio',
            'large_net_inflow',
            'large_net_ratio'
        ]

        logging.info(f"需要处理的日期: {len(dates_to_process)} 个")

        # 批量处理日期以提高性能
        batch_size = 50  # 每批处理50个日期
        for i in tqdm(range(0, len(dates_to_process), batch_size), desc="生成历史最大值排行榜"):
            batch_dates = dates_to_process[i:i + batch_size]
            generate_historical_max_ranking_for_dates_batch(batch_dates, cursor, historical_max_metrics)

        conn.commit()
        logging.info(f"增量历史最大值排行榜生成完成，处理了 {len(dates_to_process)} 个日期")

    except Exception as e:
        logging.error(f"增量生成历史最大值排行榜时出错: {e}")
        conn.rollback()
    finally:
        conn.close()


# 生成历史最大值排行榜（全量）
def generate_historical_max_rankings():
    """生成历史最大值排行榜（全量）"""
    logging.info("开始生成历史最大值排行榜...")

    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    try:
        if DATA_TYPE == 'all':
            # 获取所有日期
            cursor.execute('SELECT DISTINCT date FROM daily_fund_flow ORDER BY date')
            dates_to_process = [row[0] for row in cursor.fetchall()]
        else:  # single
            # 获取单个股票的所有日期
            cursor.execute('SELECT DISTINCT date FROM daily_fund_flow WHERE stock_code = ? ORDER BY date', (SINGLE_STOCK,))
            dates_to_process = [row[0] for row in cursor.fetchall()]

        if not dates_to_process:
            logging.info("没有数据可以生成历史最大值排行榜")
            return

        # 历史最大值排行榜的指标
        historical_max_metrics = [
            'main_net_inflow',
            'main_net_ratio',
            'super_large_net_inflow',
            'super_large_net_ratio',
            'large_net_inflow',
            'large_net_ratio'
        ]

        logging.info(f"需要处理的日期: {len(dates_to_process)} 个")

        # 批量处理日期以提高性能
        batch_size = 50  # 每批处理50个日期
        for i in tqdm(range(0, len(dates_to_process), batch_size), desc="生成历史最大值排行榜"):
            batch_dates = dates_to_process[i:i + batch_size]
            generate_historical_max_ranking_for_dates_batch(batch_dates, cursor, historical_max_metrics)

        conn.commit()
        logging.info(f"历史最大值排行榜生成完成，处理了 {len(dates_to_process)} 个日期")

    except Exception as e:
        logging.error(f"生成历史最大值排行榜时出错: {e}")
        conn.rollback()
    finally:
        conn.close()


def generate_historical_max_ranking_for_dates_batch(dates, cursor, metrics):
    """批量为多个日期生成历史最大值排行榜 - 性能优化版本"""

    if not dates:
        return

    # 将日期列表转换为SQL IN子句格式
    dates_str = "', '".join(dates)

    for metric in metrics:
        # 使用批量查询，一次性处理多个日期
        if DATA_TYPE == 'all':
            query = f'''
                WITH historical_max AS (
                    SELECT
                        stock_code,
                        date,
                        {metric} as current_value,
                        MAX({metric}) OVER (
                            PARTITION BY stock_code
                            ORDER BY date
                            ROWS BETWEEN UNBOUNDED PRECEDING AND 1 PRECEDING
                        ) as prev_max,
                        MAX({metric}) OVER (
                            PARTITION BY stock_code
                            ORDER BY date
                            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                        ) as overall_max,
                        FIRST_VALUE(date) OVER (
                            PARTITION BY stock_code
                            ORDER BY {metric} DESC, date ASC
                            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                        ) as max_date
                    FROM daily_fund_flow
                    WHERE {metric} IS NOT NULL
                ),
                ranked_data AS (
                    SELECT
                        date,
                        stock_code,
                        current_value,
                        COALESCE(prev_max, -999999999) as prev_max,
                        overall_max as historical_max_value,
                        max_date as historical_max_date,
                        CASE WHEN current_value > COALESCE(prev_max, -999999999) THEN 1 ELSE 0 END as is_historical_max,
                        ROW_NUMBER() OVER (PARTITION BY date ORDER BY current_value DESC) as rank_position
                    FROM historical_max
                    WHERE date IN ('{dates_str}')
                )
                SELECT
                    date, stock_code, current_value, prev_max, historical_max_value,
                    historical_max_date, is_historical_max, rank_position
                FROM ranked_data
                ORDER BY date, rank_position
            '''
            cursor.execute(query)
        else:  # single
            query = f'''
                WITH historical_max AS (
                    SELECT
                        stock_code,
                        date,
                        {metric} as current_value,
                        MAX({metric}) OVER (
                            PARTITION BY stock_code
                            ORDER BY date
                            ROWS BETWEEN UNBOUNDED PRECEDING AND 1 PRECEDING
                        ) as prev_max,
                        MAX({metric}) OVER (
                            PARTITION BY stock_code
                            ORDER BY date
                            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                        ) as overall_max,
                        FIRST_VALUE(date) OVER (
                            PARTITION BY stock_code
                            ORDER BY {metric} DESC, date ASC
                            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
                        ) as max_date
                    FROM daily_fund_flow
                    WHERE stock_code = ? AND {metric} IS NOT NULL
                ),
                ranked_data AS (
                    SELECT
                        date,
                        stock_code,
                        current_value,
                        COALESCE(prev_max, -999999999) as prev_max,
                        overall_max as historical_max_value,
                        max_date as historical_max_date,
                        CASE WHEN current_value > COALESCE(prev_max, -999999999) THEN 1 ELSE 0 END as is_historical_max,
                        ROW_NUMBER() OVER (PARTITION BY date ORDER BY current_value DESC) as rank_position
                    FROM historical_max
                    WHERE date IN ('{dates_str}')
                )
                SELECT
                    date, stock_code, current_value, prev_max, historical_max_value,
                    historical_max_date, is_historical_max, rank_position
                FROM ranked_data
                ORDER BY date, rank_position
            '''
            cursor.execute(query, (SINGLE_STOCK,))

        results = cursor.fetchall()

        if not results:
            continue

        # 批量插入数据
        batch_data = []
        for date, stock_code, current_value, _, historical_max_value, historical_max_date, is_historical_max, rank_position in results:
            batch_data.append((
                date, metric, rank_position,
                stock_code, '',  # 数据库中没有股票名称
                current_value, historical_max_value,
                historical_max_date, is_historical_max
            ))

        # 使用executemany进行批量插入
        cursor.executemany('''
            INSERT OR REPLACE INTO historical_max_rankings
            (date, metric, rank_position, stock_code, stock_name,
             current_value, historical_max_value, historical_max_date, is_historical_max)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', batch_data)


def generate_historical_max_ranking_for_date(date, cursor, metrics):
    """为指定日期生成历史最大值排行榜 - 兼容性保留"""
    generate_historical_max_ranking_for_dates_batch([date], cursor, metrics)


def generate_ranking_for_date(date, cursor):
    """为指定日期生成排行榜"""
    # 获取该日期的所有股票数据
    cursor.execute('''
        SELECT stock_code, main_net_inflow, main_net_ratio,
               super_large_net_inflow, super_large_net_ratio,
               large_net_inflow, large_net_ratio,
               medium_net_inflow, medium_net_ratio,
               small_net_inflow, small_net_ratio
        FROM daily_fund_flow
        WHERE date = ? AND main_net_inflow IS NOT NULL
    ''', (date,))

    stocks_data = cursor.fetchall()

    if not stocks_data:
        return

    # 转换为字典格式
    stocks_dict = []
    for row in stocks_data:
        stocks_dict.append({
            'stock_code': row[0],
            'stock_name': '',  # 数据库中没有股票名称
            'main_net_inflow': row[1],
            'main_net_ratio': row[2],
            'super_large_net_inflow': row[3],
            'super_large_net_ratio': row[4],
            'large_net_inflow': row[5],
            'large_net_ratio': row[6],
            'medium_net_inflow': row[7],
            'medium_net_ratio': row[8],
            'small_net_inflow': row[9],
            'small_net_ratio': row[10]
        })

    # 为每个指标生成排行榜
    for metric in RANKING_METRICS:
        # 按指标值排序（降序）
        sorted_stocks = sorted(stocks_dict,
                             key=lambda x: x.get(metric, float('-inf')),
                             reverse=True)

        # 保存前N名到数据库
        for rank, stock in enumerate(sorted_stocks[:RANKING_TOP_N], 1):
            cursor.execute('''
                INSERT OR REPLACE INTO daily_rankings
                (date, metric, rank_position, stock_code, stock_name, metric_value)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                date, metric, rank,
                stock['stock_code'], stock['stock_name'],
                stock.get(metric, 0)
            ))


def display_latest_rankings():
    """显示最新一天的排行榜作为示例"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    try:
        # 获取最新日期
        latest_date = get_latest_ranking_date()

        if not latest_date:
            logging.info("没有排行榜数据")
            return

        logging.info(f"\n显示 {latest_date} 的排行榜示例（前{RANKING_DISPLAY_N}名）:")

        print("\n" + "="*80)
        print(f"📊 {latest_date} 资金流向排行榜")
        print("="*80)

        for metric in RANKING_METRICS:
            # 获取该指标的排行榜
            cursor.execute('''
                SELECT rank_position, stock_code, stock_name, metric_value
                FROM daily_rankings
                WHERE date = ? AND metric = ?
                ORDER BY rank_position
                LIMIT ?
            ''', (latest_date, metric, RANKING_DISPLAY_N))

            results = cursor.fetchall()

            if results:
                # 指标名称映射
                metric_names = {
                    'main_net_inflow': '主力净流入净额',
                    'main_net_ratio': '主力净流入净占比',
                    'super_large_net_inflow': '超大单净流入净额',
                    'super_large_net_ratio': '超大单净流入净占比',
                    'large_net_inflow': '大单净流入净额',
                    'large_net_ratio': '大单净流入净占比',
                    'medium_net_inflow': '中单净流入净额',
                    'medium_net_ratio': '中单净流入净占比',
                    'small_net_inflow': '小单净流入净额',
                    'small_net_ratio': '小单净流入净占比'
                }

                print(f"\n🏆 {metric_names.get(metric, metric)} 排行榜:")
                print("-" * 60)
                print(f"{'排名':<4} {'股票代码':<8} {'股票名称':<12} {'数值':<15}")
                print("-" * 60)

                for rank, stock_code, stock_name, value in results:
                    # 检查value是否为有效数值
                    try:
                        if value == '-' or value is None or pd.isna(value):
                            value_str = '-'
                        elif 'ratio' in metric:
                            # 确保value是数值类型
                            if isinstance(value, str):
                                # 移除可能的百分号和逗号
                                clean_value = value.replace('%', '').replace(',', '')
                                value_str = f"{float(clean_value):.2f}%"
                            else:
                                value_str = f"{float(value):.2f}%"
                        else:
                            # 确保value是数值类型
                            if isinstance(value, str):
                                # 移除可能的逗号
                                clean_value = value.replace(',', '')
                                value_str = f"{float(clean_value):,.0f}"
                            else:
                                value_str = f"{float(value):,.0f}"
                    except (ValueError, TypeError):
                        value_str = '-'
                    
                    print(f"{rank:<4} {stock_code:<8} {stock_name:<12} {value_str:<15}")

    except Exception as e:
        logging.error(f"显示排行榜时出错: {e}")
    finally:
        conn.close()


def format_amount(num):
    if pd.isna(num) or num is None:
        return 'N/A'
    if abs(num) >= 1_0000_0000:
        return f"{num / 1_0000_0000:.2f}亿"
    if abs(num) >= 1_0000:
        return f"{num / 1_0000:.2f}万"
    return f"{num:.2f}"


def display_historical_max_rankings():
    """显示历史最大值排行榜"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    try:
        # 获取最新日期
        cursor.execute('SELECT MAX(date) FROM historical_max_rankings')
        latest_date = cursor.fetchone()[0]

        if not latest_date:
            logging.info("没有历史最大值排行榜数据")
            return

        logging.info(f"\n显示 {latest_date} 的历史最大值排行榜:")

        print("\n" + "="*100)
        print(f"📊 {latest_date} 历史最大值排行榜")
        print("="*100)

        # 历史最大值排行榜的指标
        historical_max_metrics = [
            'main_net_inflow',
            'main_net_ratio',
            'super_large_net_inflow',
            'super_large_net_ratio',
            'large_net_inflow',
            'large_net_ratio'
        ]

        # 指标名称映射
        metric_names = {
            'main_net_inflow': '今日主力净流入净额历史最大',
            'main_net_ratio': '今日主力净流入净占比历史最大',
            'super_large_net_inflow': '今日超大单净流入净额历史最大',
            'super_large_net_ratio': '今日超大单净流入净占比历史最大',
            'large_net_inflow': '今日大单净流入净额历史最大',
            'large_net_ratio': '今日大单净流入净占比历史最大'
        }

        for metric in historical_max_metrics:
            # 获取该指标创历史新高的股票
            cursor.execute('''
                SELECT rank_position, stock_code, stock_name, current_value,
                       historical_max_value, historical_max_date, is_historical_max
                FROM historical_max_rankings
                WHERE date = ? AND metric = ? AND is_historical_max = 1
                ORDER BY current_value DESC
                LIMIT 20
            ''', (latest_date, metric))

            results = cursor.fetchall()

            if results:
                print(f"\n🏆 {metric_names.get(metric, metric)} 排行榜:")
                print("-" * 90)
                print(f"{'排名':<4} {'股票代码':<8} {'当前值':<15} {'历史最大值':<15} {'历史最大日期':<12} {'是否新高':<8}")
                print("-" * 90)

                for rank, stock_code, _, current_value, historical_max_value, historical_max_date, is_historical_max in results:
                    is_new_high = "✅新高" if is_historical_max else "❌"

                    if 'ratio' in metric:
                        # 确保current_value和historical_max_value是数值类型
                        if isinstance(current_value, str):
                            clean_current = current_value.replace('%', '').replace(',', '')
                            current_str = f"{float(clean_current):.2f}%"
                        else:
                            current_str = f"{float(current_value):.2f}%"
                        
                        if isinstance(historical_max_value, str):
                            clean_historical = historical_max_value.replace('%', '').replace(',', '')
                            historical_str = f"{float(clean_historical):.2f}%"
                        else:
                            historical_str = f"{float(historical_max_value):.2f}%"
                    else:
                        current_str = format_amount(current_value)
                        historical_str = format_amount(historical_max_value)

                    print(f"{rank:<4} {stock_code:<8} {current_str:<15} {historical_str:<15} {historical_max_date:<12} {is_new_high:<8}")
            else:
                print(f"\n🏆 {metric_names.get(metric, metric)} 排行榜: 暂无创历史新高的股票")

        # 显示总体统计
        cursor.execute('''
            SELECT COUNT(*) as total_stocks,
                   SUM(CASE WHEN is_historical_max = 1 THEN 1 ELSE 0 END) as new_high_stocks
            FROM historical_max_rankings
            WHERE date = ?
        ''', (latest_date,))

        total_result = cursor.fetchone()
        if total_result:
            total_stocks, new_high_stocks = total_result
            print(f"\n📈 {latest_date} 总体统计:")
            print(f"   总股票数: {total_stocks}")
            print(f"   创历史新高股票数: {new_high_stocks}")
            print(f"   创新高比例: {(new_high_stocks/total_stocks*100):.2f}%" if total_stocks > 0 else "   创新高比例: 0%")

    except Exception as e:
        logging.error(f"显示历史最大值排行榜时出错: {e}")
    finally:
        conn.close()


def print_single_stock_report(stock_code):
    logging.info(f"--- 正在为股票 {stock_code} 生成分析报告 ---")
    conn = sqlite3.connect(DB_NAME)

    base_df = pd.read_sql_query(f"SELECT * FROM daily_fund_flow WHERE stock_code='{stock_code}'", conn)
    if base_df.empty:
        logging.warning(f"数据库中没有 {stock_code} 的数据，无法生成报告。")
        conn.close()
        return

    print("\n" + f"--- 股票代码: {stock_code} 资金流向分析报告 ---" + "\n")

    stats_up_df = pd.read_sql_query(f"SELECT * FROM stock_stats_up WHERE stock_code='{stock_code}'", conn)
    if not stats_up_df.empty:
        stats = stats_up_df.iloc[0]
        print(f"上涨股票平均指标（涨跌幅>0，共 {stats['record_count']} 个交易日）：")
        print(
            f"  - 平均主力净流入: {format_amount(stats['avg_main_net_inflow'])} (占比: {stats['avg_main_net_ratio']:.2f}%)")
        print(
            f"  - 平均超大单净流入: {format_amount(stats['avg_super_large_net_inflow'])} (占比: {stats['avg_super_large_net_ratio']:.2f}%)")
        print(
            f"  - 平均大单净流入: {format_amount(stats['avg_large_net_inflow'])} (占比: {stats['avg_large_net_ratio']:.2f}%)")
        print(
            f"  - 平均中单净流入: {format_amount(stats['avg_medium_net_inflow'])} (占比: {stats['avg_medium_net_ratio']:.2f}%)")
        print(
            f"  - 平均小单净流入: {format_amount(stats['avg_small_net_inflow'])} (占比: {stats['avg_small_net_ratio']:.2f}%)")

    stats_down_df = pd.read_sql_query(f"SELECT * FROM stock_stats_down WHERE stock_code='{stock_code}'", conn)
    if not stats_down_df.empty:
        stats = stats_down_df.iloc[0]
        print(f"\n下跌股票平均指标（涨跌幅<0，共 {stats['record_count']} 个交易日）：")
        print(
            f"  - 平均主力净流入: {format_amount(stats['avg_main_net_inflow'])} (占比: {stats['avg_main_net_ratio']:.2f}%)")
        print(
            f"  - 平均超大单净流入: {format_amount(stats['avg_super_large_net_inflow'])} (占比: {stats['avg_super_large_net_ratio']:.2f}%)")
        print(
            f"  - 平均大单净流入: {format_amount(stats['avg_large_net_inflow'])} (占比: {stats['avg_large_net_ratio']:.2f}%)")
        print(
            f"  - 平均中单净流入: {format_amount(stats['avg_medium_net_inflow'])} (占比: {stats['avg_medium_net_ratio']:.2f}%)")
        print(
            f"  - 平均小单净流入: {format_amount(stats['avg_small_net_inflow'])} (占比: {stats['avg_small_net_ratio']:.2f}%)")

    # 使用按年份的阈值统计数据（total年份代表所有历史数据）
    threshold_df = pd.read_sql_query(f"SELECT * FROM stock_stats_threshold_yearly WHERE stock_code='{stock_code}' AND year='total'", conn)
    if not threshold_df.empty:
        print("\n--- 特定涨跌幅资金表现 ---")
        up_10 = threshold_df[threshold_df['threshold_type'] == 'up_10']
        if not up_10.empty:
            stats = up_10.iloc[0]
            dates = base_df[base_df['change_pct'] >= 10]['date'].tolist()
            print(f"涨幅 >= 10% ({stats['record_count']}个交易日: {', '.join(dates)}):")
            print(f"  - 平均主力净流入: {format_amount(stats['avg_main_net_inflow'])}")
            print(f"  - 平均超大单净流入: {format_amount(stats['avg_super_large_net_inflow'])}")
            print(f"  - 平均大单净流入: {format_amount(stats['avg_large_net_inflow'])}")
        down_99 = threshold_df[threshold_df['threshold_type'] == 'down_9.9']
        if not down_99.empty:
            stats = down_99.iloc[0]
            dates = base_df[base_df['change_pct'] <= -9.9]['date'].tolist()
            print(f"跌幅 <= -9.9% ({stats['record_count']}个交易日: {', '.join(dates)}):")
            print(f"  - 平均主力净流入: {format_amount(stats['avg_main_net_inflow'])}")
            print(f"  - 平均超大单净流入: {format_amount(stats['avg_super_large_net_inflow'])}")
            print(f"  - 平均大单净流入: {format_amount(stats['avg_large_net_inflow'])}")

    # 使用按年份的极值统计数据（total年份代表所有历史数据）
    extremes_df = pd.read_sql_query(f"SELECT * FROM stock_stats_extremes_yearly WHERE stock_code='{stock_code}' AND year='total'", conn)
    if not extremes_df.empty:
        print("\n--- 历史资金指标极值 ---")
        metrics_map = {
            'main_net_inflow': '主力净流入净额', 'main_net_ratio': '主力净占比',
            'super_large_net_inflow': '超大单净流入净额', 'super_large_net_ratio': '超大单净占比',
            'large_net_inflow': '大单净流入净额', 'large_net_ratio': '大单净占比',
            'medium_net_inflow': '中单净流入净额', 'medium_net_ratio': '中单净占比',
            'small_net_inflow': '小单净流入净额', 'small_net_ratio': '小单净占比'
        }
        for metric, name in metrics_map.items():
            extreme_data = extremes_df[extremes_df['metric'] == metric]
            if not extreme_data.empty:
                data = extreme_data.iloc[0]
                formatter = format_amount if 'inflow' in metric else lambda x: f"{x:.2f}%"
                print(f"{name}:")
                print(f"  - 最大值: {formatter(data['max_value'])} (日期: {data['max_date']})")
                print(f"  - 最小值: {formatter(data['min_value'])} (日期: {data['min_date']})")

    conn.close()
    print("\n" + "--- 报告结束 ---" + "\n")


def print_yearly_indicators_report(stock_code):
    """打印按年份的指标报告"""
    logging.info(f"--- 正在为股票 {stock_code} 生成按年份指标报告 ---")
    conn = sqlite3.connect(DB_NAME)

    # 检查是否有数据
    base_df = pd.read_sql_query(f"SELECT * FROM daily_fund_flow WHERE stock_code='{stock_code}'", conn)
    if base_df.empty:
        logging.warning(f"数据库中没有 {stock_code} 的数据，无法生成报告。")
        conn.close()
        return

    print("\n" + f"--- 股票代码: {stock_code} 按年份指标分析报告 ---" + "\n")

    # 获取年份统计数据
    yearly_stats_df = pd.read_sql_query(f"SELECT * FROM stock_stats_yearly WHERE stock_code='{stock_code}' ORDER BY year, period_type", conn)

    if not yearly_stats_df.empty:
        print("=== 按年份涨跌平均统计 ===")

        # 按年份分组显示
        for year in yearly_stats_df['year'].unique():
            year_data = yearly_stats_df[yearly_stats_df['year'] == year]

            if year == 'total':
                print(f"\n📊 历史总计指标:")
            else:
                print(f"\n📅 {year}年指标:")

            # 上涨数据
            up_data = year_data[year_data['period_type'] == 'up']
            if not up_data.empty:
                stats = up_data.iloc[0]
                print(f"  上涨平均指标（涨跌幅>0，共 {stats['record_count']} 个交易日）：")
                print(f"    - 平均主力净流入: {format_amount(stats['avg_main_net_inflow'])} (占比: {stats['avg_main_net_ratio']:.2f}%)")
                print(f"    - 平均超大单净流入: {format_amount(stats['avg_super_large_net_inflow'])} (占比: {stats['avg_super_large_net_ratio']:.2f}%)")
                print(f"    - 平均大单净流入: {format_amount(stats['avg_large_net_inflow'])} (占比: {stats['avg_large_net_ratio']:.2f}%)")

            # 下跌数据
            down_data = year_data[year_data['period_type'] == 'down']
            if not down_data.empty:
                stats = down_data.iloc[0]
                print(f"  下跌平均指标（涨跌幅<0，共 {stats['record_count']} 个交易日）：")
                print(f"    - 平均主力净流入: {format_amount(stats['avg_main_net_inflow'])} (占比: {stats['avg_main_net_ratio']:.2f}%)")
                print(f"    - 平均超大单净流入: {format_amount(stats['avg_super_large_net_inflow'])} (占比: {stats['avg_super_large_net_ratio']:.2f}%)")
                print(f"    - 平均大单净流入: {format_amount(stats['avg_large_net_inflow'])} (占比: {stats['avg_large_net_ratio']:.2f}%)")

    # 获取年份阈值统计数据
    yearly_threshold_df = pd.read_sql_query(f"SELECT * FROM stock_stats_threshold_yearly WHERE stock_code='{stock_code}' ORDER BY year, threshold_type", conn)

    if not yearly_threshold_df.empty:
        print("\n=== 按年份特定涨跌幅资金表现 ===")

        for year in yearly_threshold_df['year'].unique():
            year_data = yearly_threshold_df[yearly_threshold_df['year'] == year]

            if year == 'total':
                print(f"\n📊 历史总计阈值指标:")
            else:
                print(f"\n📅 {year}年阈值指标:")

            # 涨幅 >= 10%
            up_10 = year_data[year_data['threshold_type'] == 'up_10']
            if not up_10.empty:
                stats = up_10.iloc[0]
                # 获取具体日期
                if year == 'total':
                    dates = base_df[base_df['change_pct'] >= 10]['date'].tolist()
                else:
                    year_base_df = base_df[pd.to_datetime(base_df['date']).dt.year == int(year)]
                    dates = year_base_df[year_base_df['change_pct'] >= 10]['date'].tolist()

                print(f"  涨幅 >= 10% ({stats['record_count']}个交易日: {', '.join(dates[:5])}{'...' if len(dates) > 5 else ''}):")
                print(f"    - 平均主力净流入: {format_amount(stats['avg_main_net_inflow'])}")
                print(f"    - 平均超大单净流入: {format_amount(stats['avg_super_large_net_inflow'])}")
                print(f"    - 平均大单净流入: {format_amount(stats['avg_large_net_inflow'])}")

            # 跌幅 <= -9.9%
            down_99 = year_data[year_data['threshold_type'] == 'down_9.9']
            if not down_99.empty:
                stats = down_99.iloc[0]
                # 获取具体日期
                if year == 'total':
                    dates = base_df[base_df['change_pct'] <= -9.9]['date'].tolist()
                else:
                    year_base_df = base_df[pd.to_datetime(base_df['date']).dt.year == int(year)]
                    dates = year_base_df[year_base_df['change_pct'] <= -9.9]['date'].tolist()

                print(f"  跌幅 <= -9.9% ({stats['record_count']}个交易日: {', '.join(dates[:5])}{'...' if len(dates) > 5 else ''}):")
                print(f"    - 平均主力净流入: {format_amount(stats['avg_main_net_inflow'])}")
                print(f"    - 平均超大单净流入: {format_amount(stats['avg_super_large_net_inflow'])}")
                print(f"    - 平均大单净流入: {format_amount(stats['avg_large_net_inflow'])}")

    # 获取年份极值统计数据
    yearly_extremes_df = pd.read_sql_query(f"SELECT * FROM stock_stats_extremes_yearly WHERE stock_code='{stock_code}' ORDER BY year, metric", conn)

    if not yearly_extremes_df.empty:
        print("\n=== 按年份历史资金指标极值 ===")

        metrics_map = {
            'main_net_inflow': '主力净流入净额', 'main_net_ratio': '主力净占比',
            'super_large_net_inflow': '超大单净流入净额', 'super_large_net_ratio': '超大单净占比',
            'large_net_inflow': '大单净流入净额', 'large_net_ratio': '大单净占比',
            'medium_net_inflow': '中单净流入净额', 'medium_net_ratio': '中单净占比',
            'small_net_inflow': '小单净流入净额', 'small_net_ratio': '小单净占比'
        }

        for year in yearly_extremes_df['year'].unique():
            year_data = yearly_extremes_df[yearly_extremes_df['year'] == year]

            if year == 'total':
                print(f"\n📊 历史总计极值:")
            else:
                print(f"\n📅 {year}年极值:")

            for metric, name in metrics_map.items():
                extreme_data = year_data[year_data['metric'] == metric]
                if not extreme_data.empty:
                    data = extreme_data.iloc[0]
                    formatter = format_amount if 'inflow' in metric else lambda x: f"{x:.2f}%"
                    print(f"  {name}:")
                    print(f"    - 最大值: {formatter(data['max_value'])} (日期: {data['max_date']})")
                    print(f"    - 最小值: {formatter(data['min_value'])} (日期: {data['min_date']})")

    conn.close()
    print("\n" + "--- 按年份报告结束 ---" + "\n")


# 主函数
def main():
    init_database()
    init_minute_database()  # 初始化分钟级数据库

    if RUN_MODE == 'ranking':
        # 排行榜模式
        logging.info("开始排行榜模式...")
        try:
            if RANKING_MODE == 'full':
                generate_daily_rankings_full()
                # 生成历史最大值排行榜（全量）
                generate_historical_max_rankings()
            else:  # incremental
                generate_daily_rankings_incremental()
                # 生成历史最大值排行榜（增量）
                generate_historical_max_rankings_incremental()

            # 生成股票历史最大值排行榜
            generate_stock_historical_max_rankings()

            # 显示最新排行榜
            display_latest_rankings()

            # 显示历史最大值排行榜
            display_historical_max_rankings()

            # 显示股票历史最大值排行榜
            display_stock_historical_max_rankings()

            logging.info("排行榜模式完成！")
        except Exception as e:
            logging.error(f"排行榜模式执行失败: {e}")
        return

    elif RUN_MODE == 'indicators':
        # 指标模式
        logging.info("开始指标模式...")
        try:
            if DATA_TYPE == 'single':
                # 为单个股票重新生成所有指标
                logging.info(f"为股票 {SINGLE_STOCK} 重新生成所有指标...")

                # 计算原有指标
                calculate_stats()
                calculate_threshold_stats()
                calculate_extremes()

                # 计算按年份指标
                calculate_yearly_indicators([SINGLE_STOCK])

                # 打印原有报告
                print_single_stock_report(SINGLE_STOCK)

                # 打印按年份报告
                print_yearly_indicators_report(SINGLE_STOCK)

                logging.info(f"股票 {SINGLE_STOCK} 指标生成完成！")

            elif DATA_TYPE == 'all':
                # 为所有股票重新生成所有指标
                logging.info("为所有股票重新生成所有指标...")

                # 计算原有指标
                calculate_stats()
                calculate_threshold_stats()
                calculate_extremes()

                # 计算按年份指标
                calculate_yearly_indicators()

                logging.info("所有股票指标生成完成！")

        except Exception as e:
            logging.error(f"指标模式执行失败: {e}")
        return

    # 原有的数据获取和计算指标模式
    update_count = 0  # 记录更新股票数
    stocks_to_compute = []

    # 获取当前日期（不含时间部分）
    today = datetime.now().strftime('%Y-%m-%d')
    # 计算前一天日期，用于交易日检查
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

    if DATA_TYPE == 'single':
        # 获取单个股票数据（增量更新）
        latest_date = get_latest_date(SINGLE_STOCK)
        if latest_date:
            logging.info(f"{SINGLE_STOCK} 最新数据日期: {latest_date}")

        # 检查日级数据是否需要更新
        if latest_date and latest_date >= today:

            logging.info(f"{SINGLE_STOCK} 日级数据已是最新（{latest_date}），无需更新")
        else:
            logging.info(f"{SINGLE_STOCK} 正在更新日级数据...")
            df = fetch_fund_flow(SINGLE_STOCK, SINGLE_MARKET, latest_date)
            if df is not None and not df.empty:
                save_to_db(df, SINGLE_STOCK)
                logging.info(f"已保存 {SINGLE_STOCK} 的资金流向数据，最新日期: {df['date'].max()}")
                update_count += 1
            else:
                logging.info(f"{SINGLE_STOCK} 无新的日级数据")

        # 检查分钟级数据是否需要更新
        if need_minute_update(SINGLE_STOCK):
            logging.info(f"{SINGLE_STOCK} 正在更新分钟级数据...")
            minute_df = fetch_minute_fund_flow(SINGLE_STOCK)
            if minute_df is not None and not minute_df.empty:
                save_minute_data_to_db(minute_df, SINGLE_STOCK)
                logging.info(f"已保存 {SINGLE_STOCK} 的分钟级数据，共 {len(minute_df)} 条记录")
            else:
                logging.info(f"{SINGLE_STOCK} 无分钟级数据")
        else:
            logging.info(f"{SINGLE_STOCK} 分钟级数据已是最新，无需更新")

        # 检查是否需要更新指标
        conn = sqlite3.connect(DB_NAME)
        cursor = conn.cursor()
        cursor.execute("SELECT last_data_update, last_stats_update FROM stock_metadata WHERE stock_code = ?",
                       (SINGLE_STOCK,))
        metadata = cursor.fetchone()
        conn.close()

        if metadata:
            last_data_update, last_stats_update = metadata
            if (update_count > 0 or last_stats_update is None or
                    (last_data_update and last_stats_update and last_data_update > last_stats_update)):
                stocks_to_compute = [SINGLE_STOCK]

        if stocks_to_compute:
            logging.info(f"开始为 {SINGLE_STOCK} 计算指标...")
            calculate_stats()
            calculate_threshold_stats()
            calculate_extremes()

            # 在所有计算完成后，更新元数据时间戳
            conn = sqlite3.connect(DB_NAME)
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE stock_metadata SET last_stats_update = ? WHERE stock_code = ?
            ''', (today, SINGLE_STOCK))
            conn.commit()
            conn.close()
            logging.info(f"为 {SINGLE_STOCK} 计算指标完成。")

            # 在指标计算完成后，增量生成排行榜
            logging.info("开始增量生成排行榜...")
            try:
                generate_daily_rankings_incremental()
                logging.info("增量排行榜生成完成")
            except Exception as e:
                logging.error(f"增量排行榜生成失败: {e}")

            # 生成历史最大值排行榜（增量）
            logging.info("开始增量生成历史最大值排行榜...")
            try:
                generate_historical_max_rankings_incremental()
                logging.info("增量历史最大值排行榜生成完成")
            except Exception as e:
                logging.error(f"增量历史最大值排行榜生成失败: {e}")

            # 生成股票历史最大值排行榜
            logging.info("开始生成股票历史最大值排行榜...")
            try:
                generate_stock_historical_max_rankings()
                logging.info("股票历史最大值排行榜生成完成")
            except Exception as e:
                logging.error(f"股票历史最大值排行榜生成失败: {e}")
        else:
            logging.info(f"{SINGLE_STOCK} 指标已是最新，无需重新计算。")

        # 无论是否计算，都打印单只股票的报告
        print_single_stock_report(SINGLE_STOCK)

    else:  # DATA_TYPE == 'all'
        stocks = get_all_stocks()

        # 初始化 stock_metadata
        conn = sqlite3.connect(DB_NAME)
        cursor = conn.cursor()
        for stock_code, _ in stocks:
            cursor.execute('''
                INSERT OR IGNORE INTO stock_metadata (stock_code)
                VALUES (?)
            ''', (stock_code,))
        conn.commit()
        conn.close()

        for i, (stock, market) in enumerate(tqdm(stocks, desc="正在更新所有股票数据...")):
            # 检查日级数据
            latest_date = get_latest_date(stock)
            if latest_date:
                logging.info(f"{stock} 最新数据日期: {latest_date}")

            if latest_date and latest_date >= today:

                logging.info(f"{stock} 日级数据已是最新（{latest_date}），无需更新")
            else:
                logging.info(f"{stock} 正在更新日级数据...")
                df = fetch_fund_flow(stock, market, latest_date)
                if df is not None and not df.empty:
                    logging.info(f"已保存 {stock} 的资金流向数据，最新日期: {df['date'].max()}")
                    save_to_db(df, stock)
                    update_count += 1
                    stocks_to_compute.append(stock)
                else:
                    logging.info(f"{stock} 无新的日级数据")

            # 检查分钟级数据
            if need_minute_update(stock):
                logging.info(f"{stock} 正在更新分钟级数据...")
                minute_df = fetch_minute_fund_flow(stock)
                if minute_df is not None and not minute_df.empty:
                    save_minute_data_to_db(minute_df, stock)
                    logging.info(f"已保存 {stock} 的分钟级数据，共 {len(minute_df)} 条记录")
                else:
                    logging.info(f"{stock} 无分钟级数据")

            time.sleep(1)  # 避免接口限频

        # 只对需要更新的股票进行指标计算
        if stocks_to_compute:
            logging.info(
                f"数据更新完成，共更新 {update_count} 只股票。开始为 {len(stocks_to_compute)} 只股票计算指标...")
            calculate_stats()
            calculate_threshold_stats()
            calculate_extremes()

            # 在所有计算完成后，统一更新元数据时间戳
            conn = sqlite3.connect(DB_NAME)
            cursor = conn.cursor()
            update_data = [(today, stock_code) for stock_code in stocks_to_compute]
            cursor.executemany('''
                UPDATE stock_metadata SET last_stats_update = ? WHERE stock_code = ?
            ''', update_data)
            conn.commit()
            conn.close()
            logging.info(f"处理完成：更新 {update_count} 只股票的数据，为 {len(stocks_to_compute)} 只股票计算指标。")

            # 在数据和指标更新完成后，增量生成排行榜
            logging.info("开始增量生成排行榜...")
            try:
                generate_daily_rankings_incremental()
                logging.info("增量排行榜生成完成")
            except Exception as e:
                logging.error(f"增量排行榜生成失败: {e}")

            # 生成历史最大值排行榜（增量）
            logging.info("开始增量生成历史最大值排行榜...")
            try:
                generate_historical_max_rankings_incremental()
                logging.info("增量历史最大值排行榜生成完成")
            except Exception as e:
                logging.error(f"增量历史最大值排行榜生成失败: {e}")

            # 生成股票历史最大值排行榜
            logging.info("开始生成股票历史最大值排行榜...")
            try:
                generate_stock_historical_max_rankings()
                logging.info("股票历史最大值排行榜生成完成")
            except Exception as e:
                logging.error(f"股票历史最大值排行榜生成失败: {e}")
        else:
            logging.info(f"无股票需要更新数据或计算指标。")

        logging.info(f"所有任务处理完成：共处理 {update_count} 只股票，计算 {len(stocks_to_compute)} 只股票的指标。")


# 生成以股票为单位的历史最大值排行榜
def generate_stock_historical_max_rankings():
    """生成以股票为单位的历史最大值排行榜 - 每个股票内部的历史排行榜"""
    logging.info("开始生成以股票为单位的历史最大值排行榜...")

    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    try:
        # 获取需要处理的股票
        if DATA_TYPE == 'all':
            cursor.execute('SELECT DISTINCT stock_code FROM daily_fund_flow')
            stocks_to_process = [row[0] for row in cursor.fetchall()]
        else:  # single
            stocks_to_process = [SINGLE_STOCK]

        if not stocks_to_process:
            logging.info("没有股票数据可以生成排行榜")
            return

        # 历史最大值排行榜的指标
        stock_max_metrics = [
            'main_net_inflow',
            'main_net_ratio',
            'super_large_net_inflow',
            'super_large_net_ratio',
            'large_net_inflow',
            'large_net_ratio'
        ]

        logging.info(f"需要处理的股票: {len(stocks_to_process)} 只")

        # 为每只股票生成历史最大值排行榜
        for stock_code in tqdm(stocks_to_process, desc="生成股票历史最大值排行榜"):
            generate_stock_historical_max_ranking_for_stock(stock_code, cursor, stock_max_metrics)

        conn.commit()
        logging.info(f"股票历史最大值排行榜生成完成，处理了 {len(stocks_to_process)} 只股票")

    except Exception as e:
        logging.error(f"生成股票历史最大值排行榜时出错: {e}")
        conn.rollback()
    finally:
        conn.close()


def generate_stock_historical_max_ranking_for_stock(stock_code, cursor, metrics):
    """为指定股票生成历史最大值排行榜 - 该股票内部的历史排行榜"""

    # 获取该股票的所有数据
    cursor.execute('''
        SELECT date, main_net_inflow, main_net_ratio,
               super_large_net_inflow, super_large_net_ratio,
               large_net_inflow, large_net_ratio
        FROM daily_fund_flow
        WHERE stock_code = ?
        ORDER BY date
    ''', (stock_code,))

    stock_data = cursor.fetchall()

    if not stock_data:
        return

    # 转换为字典格式，按年份分组
    yearly_data = {}
    all_data = []

    for row in stock_data:
        date = row[0]
        year = date[:4]  # 提取年份

        record = {
            'date': date,
            'main_net_inflow': row[1],
            'main_net_ratio': row[2],
            'super_large_net_inflow': row[3],
            'super_large_net_ratio': row[4],
            'large_net_inflow': row[5],
            'large_net_ratio': row[6]
        }

        # 按年份分组
        if year not in yearly_data:
            yearly_data[year] = []
        yearly_data[year].append(record)

        # 总数据
        all_data.append(record)

    # 清除该股票的旧排行榜数据
    cursor.execute('DELETE FROM stock_historical_max_rankings WHERE stock_code = ?', (stock_code,))

    # 为每个指标生成排行榜
    for metric in metrics:
        # 1. 为每年生成排行榜 - 该股票在该年的历史最大值前10名
        for year, year_data in yearly_data.items():
            # 过滤有效数据
            valid_data = [record for record in year_data if record[metric] is not None]
            if not valid_data:
                continue

            # 按指标值排序（降序），取前10名
            sorted_data = sorted(valid_data, key=lambda x: x[metric], reverse=True)[:10]

            # 保存到数据库
            for rank, record in enumerate(sorted_data, 1):
                cursor.execute('''
                    INSERT INTO stock_historical_max_rankings
                    (stock_code, year, metric, rank_position, value, date)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    stock_code, year, metric, rank,
                    record[metric], record['date']
                ))

        # 2. 生成总的历史排行榜 - 该股票所有历史数据的最大值前10名
        valid_all_data = [record for record in all_data if record[metric] is not None]
        if valid_all_data:
            # 按指标值排序（降序），取前10名
            sorted_all_data = sorted(valid_all_data, key=lambda x: x[metric], reverse=True)[:10]

            # 保存到数据库
            for rank, record in enumerate(sorted_all_data, 1):
                cursor.execute('''
                    INSERT INTO stock_historical_max_rankings
                    (stock_code, year, metric, rank_position, value, date)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    stock_code, 'total', metric, rank,
                    record[metric], record['date']
                ))


def display_stock_historical_max_rankings():
    """显示以股票为单位的历史最大值排行榜"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    try:
        if DATA_TYPE == 'single':
            # 单个股票模式，显示该股票的排行榜
            display_single_stock_historical_max_rankings(SINGLE_STOCK, cursor)
        else:
            # 所有股票模式，显示所有股票的排行榜概览
            display_all_stocks_historical_max_rankings_summary(cursor)

    except Exception as e:
        logging.error(f"显示股票历史最大值排行榜时出错: {e}")
    finally:
        conn.close()


def display_single_stock_historical_max_rankings(stock_code, cursor):
    """显示单个股票的历史最大值排行榜"""

    # 检查是否有该股票的数据
    cursor.execute('SELECT COUNT(*) FROM stock_historical_max_rankings WHERE stock_code = ?', (stock_code,))
    count = cursor.fetchone()[0]

    if count == 0:
        logging.info(f"没有股票 {stock_code} 的历史最大值排行榜数据")
        return

    print("\n" + "="*100)
    print(f"📊 股票 {stock_code} 历史最大值排行榜")
    print("="*100)

    # 股票历史最大值排行榜的指标
    stock_max_metrics = [
        'main_net_inflow',
        'main_net_ratio',
        'super_large_net_inflow',
        'super_large_net_ratio',
        'large_net_inflow',
        'large_net_ratio'
    ]

    # 指标名称映射
    metric_names = {
        'main_net_inflow': '主力净流入净额历史最大',
        'main_net_ratio': '主力净流入净占比历史最大',
        'super_large_net_inflow': '超大单净流入净额历史最大',
        'super_large_net_ratio': '超大单净流入净占比历史最大',
        'large_net_inflow': '大单净流入净额历史最大',
        'large_net_ratio': '大单净流入净占比历史最大'
    }

    # 获取该股票的年份
    cursor.execute('''
        SELECT DISTINCT year FROM stock_historical_max_rankings
        WHERE stock_code = ? AND year != 'total'
        ORDER BY year
    ''', (stock_code,))
    years = [row[0] for row in cursor.fetchall()]

    for metric in stock_max_metrics:
        print(f"\n🏆 {metric_names.get(metric, metric)} 排行榜:")
        print("-" * 90)

        # 显示总的历史排行榜
        cursor.execute('''
            SELECT rank_position, value, date
            FROM stock_historical_max_rankings
            WHERE stock_code = ? AND year = 'total' AND metric = ?
            ORDER BY rank_position
        ''', (stock_code, metric))

        total_results = cursor.fetchall()

        if total_results:
            print(f"📈 总历史排行榜:")
            print(f"{'排名':<4} {'股票代码':<8} {'历史最大值':<15} {'历史最大日期':<12}")
            print("-" * 50)

            for rank, value, date in total_results:
                if 'ratio' in metric:
                    # 确保value是数值类型
                    if isinstance(value, str):
                        clean_value = value.replace('%', '').replace(',', '')
                        value_str = f"{float(clean_value):.2f}%"
                    else:
                        value_str = f"{float(value):.2f}%"
                else:
                    value_str = format_amount(value)

                print(f"{rank:<4} {stock_code:<8} {value_str:<15} {date:<12}")

        # 显示各年份的排行榜
        for year in years:
            cursor.execute('''
                SELECT rank_position, value, date
                FROM stock_historical_max_rankings
                WHERE stock_code = ? AND year = ? AND metric = ?
                ORDER BY rank_position
                LIMIT 5
            ''', (stock_code, year, metric))

            year_results = cursor.fetchall()

            if year_results:
                print(f"\n📅 {year}年排行榜（前5名）:")
                print(f"{'排名':<4} {'股票代码':<8} {'历史最大值':<15} {'历史最大日期':<12}")
                print("-" * 50)

                for rank, value, date in year_results:
                    if 'ratio' in metric:
                        # 确保value是数值类型
                        if isinstance(value, str):
                            clean_value = value.replace('%', '').replace(',', '')
                            value_str = f"{float(clean_value):.2f}%"
                        else:
                            value_str = f"{float(value):.2f}%"
                    else:
                        value_str = format_amount(value)

                    print(f"{rank:<4} {stock_code:<8} {value_str:<15} {date:<12}")


def display_all_stocks_historical_max_rankings_summary(cursor):
    """显示所有股票的历史最大值排行榜概览"""

    print("\n" + "="*100)
    print(f"📊 所有股票历史最大值排行榜概览")
    print("="*100)

    # 统计信息
    cursor.execute('SELECT COUNT(DISTINCT stock_code) FROM stock_historical_max_rankings')
    stock_count = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(DISTINCT year) FROM stock_historical_max_rankings WHERE year != "total"')
    year_count = cursor.fetchone()[0]

    print(f"📈 统计信息:")
    print(f"   处理股票数: {stock_count}")
    print(f"   覆盖年份数: {year_count}")

    # 显示各指标的全市场最大值
    stock_max_metrics = [
        'main_net_inflow',
        'main_net_ratio',
        'super_large_net_inflow',
        'super_large_net_ratio',
        'large_net_inflow',
        'large_net_ratio'
    ]

    metric_names = {
        'main_net_inflow': '主力净流入净额',
        'main_net_ratio': '主力净流入净占比',
        'super_large_net_inflow': '超大单净流入净额',
        'super_large_net_ratio': '超大单净流入净占比',
        'large_net_inflow': '大单净流入净额',
        'large_net_ratio': '大单净流入净占比'
    }

    print(f"\n🏆 全市场历史最大值排行榜（各指标第一名）:")
    print("-" * 80)
    print(f"{'指标':<20} {'股票代码':<8} {'最大值':<15} {'日期':<12}")
    print("-" * 80)

    for metric in stock_max_metrics:
        cursor.execute('''
            SELECT stock_code, MAX(value) as max_value, date
            FROM stock_historical_max_rankings
            WHERE year = 'total' AND metric = ? AND rank_position = 1
            GROUP BY stock_code
            ORDER BY max_value DESC
            LIMIT 1
        ''', (metric,))

        result = cursor.fetchone()
        if result:
            stock_code, max_value, date = result

            if 'ratio' in metric:
                # 确保max_value是数值类型
                if isinstance(max_value, str):
                    clean_value = max_value.replace('%', '').replace(',', '')
                    value_str = f"{float(clean_value):.2f}%"
                else:
                    value_str = f"{float(max_value):.2f}%"
            else:
                value_str = format_amount(max_value)

            print(f"{metric_names.get(metric, metric):<20} {stock_code:<8} {value_str:<15} {date:<12}")


if __name__ == '__main__':
    main()