#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test the fixed pandas Series issue
"""

import sys
import os
import importlib

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import and reload the module to get latest changes
import dynamic_gap_detector
importlib.reload(dynamic_gap_detector)

def test_pandas_fix():
    """Test with pandas Series fix"""
    
    test_stocks = [
        ("药明康德", "603259"),  
        ("药明康德", "603259.SH"),  
    ]
    
    print("=== Test Pandas Series Fix ===")
    
    for stock_name, stock_code in test_stocks:
        print(f"\n测试股票: {stock_name} ({stock_code})")
        try:
            result = dynamic_gap_detector.get_stock_sectors(stock_name, stock_code)
            print(f"结果: {result}")
            
            if result['concepts']:
                print(f"  概念数量: {len(result['concepts'])}")
                print(f"  概念列表: {result['concepts'][:3]}")
            else:
                print("  [x] 没有找到概念数据")
                
            if result['industries']:
                print(f"  行业数量: {len(result['industries'])}")
                print(f"  行业列表: {result['industries']}")
            else:
                print("  [x] 没有找到行业数据")
                
        except Exception as e:
            print(f"  [x] 错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_pandas_fix()