#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Check PKL file content to understand the format
"""

import pickle
import pandas as pd

try:
    with open('../cache/stock_board_mapping.pkl', 'rb') as f:
        mapping_df = pickle.load(f)
    
    print("=== PKL文件内容检查 ===")
    print(f"数据类型: {type(mapping_df)}")
    print(f"数据形状: {mapping_df.shape}")
    print(f"列名: {mapping_df.columns.tolist()}")
    print(f"前5行数据:")
    print(mapping_df.head())
    
    # 检查是否有药明康德的数据
    if '代码' in mapping_df.columns:
        test_codes = ['603259', '603259.SH']
        for code in test_codes:
            # 使用修复后的方法
            code_mask = mapping_df['代码'] == code
            matches = mapping_df.loc[code_mask]
            print(f"\n代码 {code} 的匹配结果:")
            print(f"  匹配数量: {len(matches)}")
            if len(matches) > 0:
                print(f"  前几条: {matches.head()}")
    
except Exception as e:
    print(f"读取PKL文件失败: {e}")
    import traceback
    traceback.print_exc()