#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test all the pandas Series fixes
"""

import sys
import os
import importlib
import pandas as pd

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import and reload the module to get latest changes
import dynamic_gap_detector
importlib.reload(dynamic_gap_detector)

def test_all_pandas_fixes():
    """Test all pandas Series fixes"""
    
    # Create some mock data to test the functions
    mock_market_data = pd.DataFrame({
        '名称': ['海昌新材', '翰宇药业', '腾远钴业', '石头科技'],
        '今日主力净流入-净额': [1000000, 2000000, 1500000, 3000000],
        '今日主力净流入-净占比': [0.05, 0.08, 0.06, 0.10]
    })
    
    # Test the detector
    detector = dynamic_gap_detector.StockFlowIgnitionDetector()
    
    test_stocks = [
        ("海昌新材", "300885"),
        ("翰宇药业", "300199"),
        ("腾远钴业", "301219"),
        ("石头科技", "688169"),
    ]
    
    print("=== Test All Pandas Series Fixes ===")
    
    for stock_name, stock_code in test_stocks:
        print(f"\n测试股票: {stock_name} ({stock_code})")
        
        # Test board info method
        try:
            result = detector._get_stock_sector_info(stock_name, stock_code)
            print(f"  板块信息: 成功获取 - sectors={len(result.get('sectors', []))} 个")
        except Exception as e:
            print(f"  板块信息错误: {e}")
            if "ambiguous" in str(e):
                print("  ❌ 仍然存在pandas Series布尔值歧义问题!")
        
        # Test sector leadership analysis
        try:
            sector_info = {'sectors': ['概念1', '概念2']}
            leadership_result = detector._analyze_sector_leadership(
                stock_name, stock_code, mock_market_data, sector_info
            )
            print(f"  板块龙头分析: 成功")
        except Exception as e:
            print(f"  板块龙头分析错误: {e}")
            if "ambiguous" in str(e):
                print("  ❌ 仍然存在pandas Series布尔值歧义问题!")

if __name__ == "__main__":
    test_all_pandas_fixes()