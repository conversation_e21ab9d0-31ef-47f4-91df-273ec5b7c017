#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Reproduce the exact error by calling the exact same function
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Try to reproduce the exact error
try:
    from dynamic_gap_detector import get_stock_sectors
    
    # Test with the exact stocks mentioned in the error
    test_stocks = [
        ("海昌新材", "300885"),
        ("翰宇药业", "300199"), 
        ("腾远钴业", "301219"),
        ("石头科技", "688169")
    ]
    
    print("=== Reproducing the exact error ===")
    
    for stock_name, stock_code in test_stocks:
        print(f"\n测试 {stock_name} ({stock_code}):")
        try:
            result = get_stock_sectors(stock_name, stock_code)
            print(f"  成功: {result}")
        except Exception as e:
            print(f"  错误: {e}")
            if "ambiguous" in str(e):
                print("  ❌ 找到了pandas Series布尔值歧义错误!")
                
                # Print the full traceback to see exactly where it happens
                import traceback
                print("  完整错误堆栈:")
                traceback.print_exc()
                print()
                
except ImportError as e:
    print(f"导入错误: {e}")