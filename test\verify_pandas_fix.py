#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证v10_突破信号.py中的pandas错误是否真的修复了
"""

import sys
import os
import pandas as pd
import traceback

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_pandas_fix():
    """测试pandas修复是否有效"""
    
    print("=== 检查pandas错误修复情况 ===\n")
    
    try:
        # 导入模块
        from v10_突破信号 import StockFlowIgnitionDetector
        print("SUCCESS: 成功导入 StockFlowIgnitionDetector")
        
        # 创建检测器实例
        detector = StockFlowIgnitionDetector()
        print("SUCCESS: 成功创建检测器实例")
        
        # 创建模拟的stock_board_mapping数据 - 与实际数据格式完全一致
        mock_mapping = pd.DataFrame({
            '代码': ['300885', '300199', '301219', '688169', '600579'],
            '概念名称': [
                ['专用设备', '新材料'], 
                ['化学制药', '创新药'], 
                ['小金属', '新材料'],
                ['人工智能', '芯片概念'],
                ['专用设备', '国企改革']
            ]
        })
        detector.stock_board_mapping = mock_mapping
        print("SUCCESS: 设置模拟股票板块映射数据")
        
        # 测试修复的方法 - 用实际错误日志中提到的股票
        test_stocks = [
            ("海昌新材", "300885"),
            ("翰宇药业", "300199"), 
            ("腾远钴业", "301219"),
            ("石头科技", "688169"),
            ("中化装备", "600579")
        ]
        
        print("\n--- 测试修复后的 _get_stock_sector_info 方法 ---")
        success_count = 0
        total_count = len(test_stocks)
        
        for stock_name, stock_code in test_stocks:
            try:
                result = detector._get_stock_sector_info(stock_name, stock_code)
                print(f"SUCCESS: {stock_name}({stock_code}) -> {result}")
                success_count += 1
            except Exception as e:
                if "ambiguous" in str(e):
                    print(f"FAILED: {stock_name}({stock_code}) -> pandas Series布尔值歧义错误仍然存在: {e}")
                else:
                    print(f"WARNING: {stock_name}({stock_code}) -> 其他错误: {e}")
        
        print(f"\n=== 测试结果统计 ===")
        print(f"成功: {success_count}/{total_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        if success_count == total_count:
            print("CONCLUSION: pandas Series布尔值歧义错误已完全修复！")
            return True
        else:
            print("CONCLUSION: 仍有部分问题未解决")
            return False
            
    except Exception as e:
        print(f"FAILED: 测试过程中发生错误: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_pandas_fix()