#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟实际运行场景来测试pandas修复
"""

import sys
import os
import pandas as pd

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def simulate_real_usage():
    """模拟实际使用场景"""
    
    print("=== 模拟实际运行场景测试 ===\n")
    
    try:
        from v10_突破信号 import StockFlowIgnitionDetector
        
        # 创建检测器
        detector = StockFlowIgnitionDetector()
        
        # 模拟真实的股票板块映射数据结构
        real_like_mapping = pd.DataFrame({
            '代码': ['300885', '300199', '301219', '688169', '600579', '002028', '000001'],
            '概念名称': [
                ['专用设备', '新材料', '预盈预增'], 
                ['化学制药', '创新药', '医疗器械'], 
                ['小金属', '新材料', '有色金属'],
                ['人工智能', '芯片概念', '半导体'],
                ['专用设备', '国企改革', '央企重组'],
                ['房地产', 'REITS', '租售同权'],
                ['银行', '深圳本地', '金融科技']
            ]
        })
        detector.stock_board_mapping = real_like_mapping
        
        print("模拟处理 '个股资金流入 Top 50' 场景...")
        
        # 模拟日志中出现的实际股票
        error_stocks = [
            ("上海电气", "601727"),  # 可能不在我们的测试数据中
            ("用友网络", "600588"),  # 可能不在我们的测试数据中  
            ("沃格光电", "603773"),  # 可能不在我们的测试数据中
            ("海昌新材", "300885"),  # 在测试数据中
            ("翰宇药业", "300199"),  # 在测试数据中
            ("华银电力", "600744"),  # 可能不在我们的测试数据中
        ]
        
        pandas_errors = 0
        other_errors = 0
        success_count = 0
        
        for stock_name, stock_code in error_stocks:
            try:
                print(f"处理股票: {stock_name} ({stock_code})")
                result = detector._get_stock_sector_info(stock_name, stock_code)
                print(f"  -> SUCCESS: {result.get('sectors', [])}")
                success_count += 1
            except Exception as e:
                error_msg = str(e)
                if "ambiguous" in error_msg:
                    print(f"  -> PANDAS ERROR: The truth value of a Series is ambiguous")
                    pandas_errors += 1
                else:
                    print(f"  -> OTHER ERROR: {error_msg}")
                    other_errors += 1
        
        print(f"\n=== 测试结果 ===")
        print(f"成功处理: {success_count}")
        print(f"Pandas错误: {pandas_errors}")
        print(f"其他错误: {other_errors}")
        
        if pandas_errors == 0:
            print("\n✓ CONFIRMED: pandas Series布尔值歧义错误已完全修复!")
            print("之前日志中的错误:")
            print("'获取股票XXX板块信息失败: The truth value of a Series is ambiguous'")
            print("应该不会再出现了。")
        else:
            print(f"\n✗ WARNING: 仍有{pandas_errors}个pandas错误")
            
        return pandas_errors == 0
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    simulate_real_usage()