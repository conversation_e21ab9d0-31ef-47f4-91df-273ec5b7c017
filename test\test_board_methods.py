#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test the pandas Series fix for board info methods
"""

import sys
import os
import importlib

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import and reload the module to get latest changes
import dynamic_gap_detector
importlib.reload(dynamic_gap_detector)

def test_board_info_methods():
    """Test board info methods with pandas fix"""
    
    # Create detector instance
    detector = dynamic_gap_detector.StockFlowIgnitionDetector()
    
    test_stocks = [
        ("海昌新材", "300885"),
        ("翰宇药业", "300199"),
    ]
    
    print("=== Test Board Info Methods Fix ===")
    
    for stock_name, stock_code in test_stocks:
        print(f"\n测试股票: {stock_name} ({stock_code})")
        
        # Test the method
        try:
            result = detector._get_stock_sector_info(stock_name, stock_code)
            print(f"  板块信息结果: {result}")
        except Exception as e:
            print(f"  板块信息错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_board_info_methods()