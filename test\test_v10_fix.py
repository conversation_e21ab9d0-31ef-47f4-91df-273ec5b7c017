#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test the v10_突破信号.py pandas fix
"""

import sys
import os
import pandas as pd
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # Import the module to check for syntax errors
    import importlib.util
    spec = importlib.util.spec_from_file_location("v10_突破信号", "v10_突破信号.py")
    v10_module = importlib.util.module_from_spec(spec)
    
    print("=== Testing v10_突破信号.py Import ===")
    try:
        spec.loader.exec_module(v10_module)
        print("SUCCESS: 模块导入成功，没有语法错误")
    except Exception as e:
        print(f"FAILED: 模块导入失败: {e}")
        sys.exit(1)
    
    # Test if StockFlowIgnitionDetector class exists and can be instantiated
    print("\n=== Testing StockFlowIgnitionDetector Class ===")
    try:
        # Get all classes that might be the detector
        detector_classes = []
        for name in dir(v10_module):
            obj = getattr(v10_module, name)
            if hasattr(obj, '__class__') and hasattr(obj, '__name__') and 'Detector' in str(obj):
                detector_classes.append(name)
        
        print(f"找到的检测器类: {detector_classes}")
        
        # Try to find and test a detector class with _get_stock_sector_info method
        detector = None
        for class_name in detector_classes:
            try:
                cls = getattr(v10_module, class_name)
                if hasattr(cls, '_get_stock_sector_info'):
                    detector = cls()
                    print(f"SUCCESS: 成功创建 {class_name} 实例")
                    break
            except Exception as e:
                print(f"WARNING: 无法创建 {class_name}: {e}")
        
        if not detector:
            print("WARNING: 没有找到适合的检测器类进行测试")
        else:
            # Test the fixed method with mock data
            print("\n=== Testing Fixed _get_stock_sector_info Method ===")
            try:
                # Create mock stock_board_mapping
                mock_data = pd.DataFrame({
                    '代码': ['300885', '300199', '301219'],
                    '概念名称': [['专用设备', '新材料'], ['化学制药', '创新药'], ['小金属', '新材料']]
                })
                detector.stock_board_mapping = mock_data
                
                # Test the method
                result = detector._get_stock_sector_info("海昌新材", "300885")
                print(f"SUCCESS: _get_stock_sector_info 方法测试成功: {result}")
                
            except Exception as e:
                if "ambiguous" in str(e):
                    print(f"FAILED: 仍然存在pandas Series布尔值歧义问题: {e}")
                else:
                    print(f"WARNING: 其他错误 (可能正常): {e}")
    
    except Exception as e:
        print(f"FAILED: 检测器类测试失败: {e}")
        
    print("\n=== 测试完成 ===")
        
except ImportError as e:
    print(f"导入错误: {e}")